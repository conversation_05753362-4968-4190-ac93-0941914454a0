name: Build and Publish
#on:
#  push:
#    branches:
#      - main
jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'adopt'
          cache: 'gradle'

      - name: Build with <PERSON>rad<PERSON>
        run: ./gradlew bootJar

      - name: Deploy to ECS
        uses: appleboy/scp-action@v1.0.0
        with:
          host: ${{ secrets.ECS_HOST }}
          username: root
          password: ${{ secrets.ECS_PASSWORD }}
          source: "./build/libs/*.jar"
          target: "/root/deploy/test_action/"
          strip_components: 1

      - name: kill old Application
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.ECS_HOST }}
          username: root
          password: ${{ secrets.ECS_PASSWORD }}
          script: |
            pkill -f terra-web-0.0.1-SNAPSHOT.jar

      - name: Restart Application
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.ECS_HOST }}
          username: root
          password: ${{ secrets.ECS_PASSWORD }}
          script: |
            nohup java -Dfile.encoding=utf-8 -jar /root/deploy/test_action/libs/terra-web-0.0.1-SNAPSHOT.jar --spring.profiles.active=test >/dev/null 2>&1 &

      - name: "Pre Release"
        uses: "marvinpinto/action-automatic-releases@latest"
        with:
          repo_token: "${{ secrets.GH_TOKEN }}"
          automatic_release_tag: "latest"
          prerelease: true
          title: "Pre Build"
          files: |
            ./build/libs/*.jar