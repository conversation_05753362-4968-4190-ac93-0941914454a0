name: "tagged-release"

on:
  push:
    tags:
      - "v*"

jobs:
  tagged-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'adopt'
          cache: 'gradle'

      - name: Build with Gradle
        run: ./gradlew bootJar

      - uses: "marvinpinto/action-automatic-releases@latest"
        with:
          repo_token: "${{ secrets.GH_TOKEN }}"
          prerelease: false
          files: |
            ./build/libs/*.jar