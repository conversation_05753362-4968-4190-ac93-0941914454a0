#!/bin/bash

# 服务器信息
SERVER_IP="***********"
USERNAME="root"
PASSWORD="Ac@20252025"
REMOTE_PATH="/root/deploy/prod/jar/"

JAR_SUFFIX=$(date +"%y%m%d-%H%M")
ORIGINAL_JAR_NAME="terra-web-0.0.1-SNAPSHOT.jar"
FINAL_JAR_NAME="terra-web-prod-${JAR_SUFFIX}.jar"

# 打包 Java 应用
echo "Building Java application..."
./gradlew bootJar || { echo "Build failed!"; exit 1; }

# 重命名 JAR 包
echo "Renaming JAR..."
mv ./build/libs/$ORIGINAL_JAR_NAME ./build/libs/$FINAL_JAR_NAME || { echo "Rename failed!"; exit 1; }

# 确认重命名结果
if [ -f "./build/libs/$FINAL_JAR_NAME" ]; then
    echo "JAR renamed successfully to $FINAL_JAR_NAME"
else
    echo "Error: JAR file $FINAL_JAR_NAME not found after rename!"
    exit 1
fi

# 查找并杀死远程服务器上的进程
PROCESS_ID=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no $USERNAME@$SERVER_IP "pgrep -f terra-web-prod")
if [ -n "$PROCESS_ID" ]; then
  echo "Stopping process $PROCESS_ID..."
  sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no $USERNAME@$SERVER_IP "kill -9 $PROCESS_ID"
else
  echo "No running process found."
fi

# 传输新的 JAR 包到服务器
echo "Transferring new JAR file..."
sshpass -p "$PASSWORD" scp -o StrictHostKeyChecking=no ./build/libs/$FINAL_JAR_NAME $USERNAME@$SERVER_IP:$REMOTE_PATH

# 启动新的 JAR 包
echo "Starting new application..."
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no $USERNAME@$SERVER_IP "nohup java -Dfile.encoding=utf-8 -jar $REMOTE_PATH$FINAL_JAR_NAME --spring.profiles.active=prod >/dev/null 2>&1 &"

echo "Deployment completed."

