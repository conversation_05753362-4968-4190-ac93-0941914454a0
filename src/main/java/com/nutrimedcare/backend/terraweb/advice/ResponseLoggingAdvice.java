package com.nutrimedcare.backend.terraweb.advice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrimedcare.backend.terraweb.interceptor.LoggingInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class ResponseLoggingAdvice implements ResponseBodyAdvice<Object> {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class selectedConverterType, org.springframework.http.server.ServerHttpRequest request,
                                  org.springframework.http.server.ServerHttpResponse response) {
        try {
            String responseBody = objectMapper.writeValueAsString(body);
            LoggingInterceptor.RESPONSE_BODY_THREAD_LOCAL.set(responseBody);
        } catch (Exception e) {
            log.error("Failed to serialize responseBody", e);
        }
        return body;
    }

}