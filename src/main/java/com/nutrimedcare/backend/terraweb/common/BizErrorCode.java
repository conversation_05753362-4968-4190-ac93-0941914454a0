package com.nutrimedcare.backend.terraweb.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BizErrorCode {

    NFC_CHECK_IN_NO_BIND_MEDICINE_RECORD("001001", "当前老人尚未绑定服药单"),
    NFC_CHECK_IN_NO_NEED_TAKE("001002", "当前老人在此时段无服药计划"),
    NFC_CHECK_IN_REPEAT_TAKE("001003", "当前老人已服药，请勿重复提交"),
    NFC_CHECK_IN_NOT_BIND_CAREGIVER("001004", "当前老人不属于您的管辖范围内，请核实！"),

    ;

    private final String code;
    private final String message;

} 