package com.nutrimedcare.backend.terraweb.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    SUCCESS("200", "success"),

    FAILED("500", "操作失败"),

    VALIDATE_FAILED("400", "参数检验失败"),

    UNAUTHORIZED("401", "token已过期, 请尝试重新登录"),

    SERVER_ERROR("500", "服务器内部错误"),
    ;


    private final String code;
    private final String message;

} 