package com.nutrimedcare.backend.terraweb.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("NutrimedCare API 文档")
                        .description("营养医疗护理系统 API 接口文档")
                        .version("0.0.1")
                        .contact(new Contact()
                                .name("开发团队")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("http://www.apache.org/licenses/LICENSE-2.0.html")))
                .servers(List.of(
                        new Server().url("http://localhost:8080").description("本地环境"),
                        new Server().url("http://dev-api.nutrimedcare.com").description("开发环境")
                ));
    }
} 