package com.nutrimedcare.backend.terraweb.config;

import com.nutrimedcare.backend.terraweb.interceptor.DisableInterceptor;
import com.nutrimedcare.backend.terraweb.interceptor.JwtInterceptor;
import com.nutrimedcare.backend.terraweb.interceptor.LoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private JwtInterceptor jwtInterceptor;

    @Autowired
    private LoggingInterceptor loggingInterceptor;

    @Autowired
    private DisableInterceptor disableInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(disableInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/emergency/**");

        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/emergency/**")
                .excludePathPatterns("/login/**")
                .excludePathPatterns("/institution/login");

        registry.addInterceptor(loggingInterceptor).addPathPatterns("/**");
    }

} 