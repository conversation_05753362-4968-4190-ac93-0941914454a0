package com.nutrimedcare.backend.terraweb.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@RestController
public class APIControlController {

    private final AtomicBoolean isEnabled = new AtomicBoolean(true);

    @GetMapping("/emergency/api/enable")
    public String enableApp() {
        if (isEnabled.compareAndSet(false, true)) {
            return "Application is now enabled";
        }
        return "Application is already enabled";
    }

    @GetMapping("/emergency/api/disable")
    public String disableApp() {
        if (isEnabled.compareAndSet(true, false)) {
            return "Application is now disabled";
        }
        return "Application is already disabled";
    }

    @GetMapping("/emergency/api/status")
    public String checkStatus() {
        return isEnabled.get() ? "Application is enabled" : "Application is disabled";
    }

    public boolean isEnabled() {
        return isEnabled.get();
    }

    public boolean enable() {
        return isEnabled.compareAndSet(false, true);
    }

}