package com.nutrimedcare.backend.terraweb.controller;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.CareInstitutionManager;
import com.nutrimedcare.backend.terraweb.manager.LoginManager;
import com.nutrimedcare.backend.terraweb.param.insitution.InstitutionSaveParam;
import com.nutrimedcare.backend.terraweb.param.login.LoginParam;
import com.nutrimedcare.backend.terraweb.vo.institution.InstitutionDetailVO;
import com.nutrimedcare.backend.terraweb.vo.login.LoginVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "机构", description = "机构信息管理接口")
@Validated
@RestController
@RequestMapping("/institution")
public class InstitutionController {

    @Autowired
    private LoginManager loginManager;

    @Autowired
    private CareInstitutionManager careInstitutionManager;

    @Operation(summary = "机构登陆", description = "允许多登")
    @PostMapping("/login")
    public Result<LoginVO> login(@Valid @RequestBody LoginParam request) {
        return Result.success(loginManager.login(request));
    }

    @Operation(summary = "机构详情", description = "包括分数、药品数等信息")
    @GetMapping("/detail")
    public Result<InstitutionDetailVO> detail(@NotNull(message = "请传入机构信息") Long id) {
        return Result.success(careInstitutionManager.detail(id));
    }

    /*
     ******************************************************************
     */

    @PostMapping("/emergency/insert")
    public Result<Boolean> insert(@RequestBody InstitutionSaveParam param) {
        return Result.success(careInstitutionManager.insert(param));
    }

    @PostMapping("/emergency/update")
    public Result<Boolean> update(@RequestBody InstitutionSaveParam param) {
        return Result.success(careInstitutionManager.update(param));
    }

} 