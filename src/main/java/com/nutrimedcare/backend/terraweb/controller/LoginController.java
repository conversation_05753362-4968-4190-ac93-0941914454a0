package com.nutrimedcare.backend.terraweb.controller;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.LoginManager;
import com.nutrimedcare.backend.terraweb.param.login.Login4EaseParam;
import com.nutrimedcare.backend.terraweb.vo.login.LoginVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/login")
public class LoginController {

    @Autowired
    private LoginManager loginManager;

    @PostMapping("/ease")
    public Result<LoginVO> login(@Valid @RequestBody Login4EaseParam request) {
        return Result.success(loginManager.login4Ease(request));
    }

} 