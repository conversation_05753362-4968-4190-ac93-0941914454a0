package com.nutrimedcare.backend.terraweb.controller;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.ResidentInfoManager;
import com.nutrimedcare.backend.terraweb.param.IdParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentCheckInDataListParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentListParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentSaveParam;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentCheckIntInfoVO;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "入住人", description = "入住人信息管理接口")
@Validated
@RestController
@RequestMapping("/resident")
public class ResidentInfoController {

    @Autowired
    private ResidentInfoManager residentInfoManager;

    @Operation(summary = "入住人列表", description = "查询机构下所有入住人")
    @GetMapping("/list")
    public Result<List<ResidentInfoVO>> list(@Valid ResidentListParam param) {
        return Result.success(residentInfoManager.list(param));
    }

    @Operation(summary = "入住人搜索结果列表", description = "根据前 n 个字查询机构下入住人，只返回 id、姓名")
    @GetMapping("/searchRes")
    public Result<List<ResidentInfoVO>> searchRes(@Valid ResidentListParam param) {
        return Result.success(residentInfoManager.searchRes(param));
    }

    @GetMapping("/simpleList")
    public Result<List<ResidentInfoVO>> simpleList(@Valid ResidentListParam param) {
        return Result.success(residentInfoManager.simpleList(param));
    }

    @Operation(summary = "入住者详情", description = "根据ID查询入住者详细信息")
    @GetMapping("/detail")
    public Result<ResidentInfoVO> detail(@NotNull(message = "入住人 id 为空") Long id,
                                         @NotNull(message = "药物参数为空") Integer needMedicationRecord) {
        return Result.success(residentInfoManager.detail(id, needMedicationRecord));
    }

    @Operation(summary = "创建入住者", description = "创建新的入住者信息")
    @PostMapping("/save")
    public Result<ResidentInfoVO> save(@Valid @RequestBody ResidentSaveParam param) {
        return Result.success(residentInfoManager.save(param));
    }

    @Operation(summary = "删除入住者")
    @PostMapping("/delete")
    public Result<Boolean> delete(@Valid @RequestBody IdParam param) {
        return Result.success(residentInfoManager.delete(param));
    }

    /**
     * ------------------------------- ease app -------------------------------
     */

    @GetMapping("/list4EaseTab")
    public Result<List<ResidentInfoVO>> list4EaseTab(@Valid ResidentListParam param) {
        return Result.success(residentInfoManager.list4EaseTab(param));
    }

    @GetMapping("/detail4Ease")
    public Result<ResidentInfoVO> detail4Ease(@NotNull(message = "入住人 id 为空") Long id) {
        return Result.success(residentInfoManager.detail4Ease(id));
    }

    @GetMapping("/listCheckInInfo")
    public Result<List<ResidentCheckIntInfoVO>> listCheckInInfo(ResidentCheckInDataListParam param) {
        return Result.success(residentInfoManager.listCheckInInfo(param));
    }

} 