package com.nutrimedcare.backend.terraweb.controller;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.enums.UploadTypeEnum;
import com.nutrimedcare.backend.terraweb.util.AliyunOssUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Tag(name = "<PERSON>yun OSS")
@Validated
@RestController
@RequestMapping("/oss")
public class UploadController {

    @Autowired
    private AliyunOssUtil aliyunOssUtil;

    @Operation(summary = "上传接口")
    @PostMapping("/upload")
    public Result<String> upload(@RequestParam("file") MultipartFile file, @Valid @NotNull Integer type) {
        if (file.isEmpty()) {
            return Result.error("文件不能为空");
        }
        try {
            UploadTypeEnum uploadType = UploadTypeEnum.getByType(type);
            String fileUrl = aliyunOssUtil.uploadFile(file.getInputStream(), file.getOriginalFilename(), uploadType.getPrefix());
            return Result.success(fileUrl);
        } catch (Exception e) {
            return Result.error("上传失败");
        }
    }

}
