package com.nutrimedcare.backend.terraweb.controller.caregiver;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.caregiver.CaregiverManager;
import com.nutrimedcare.backend.terraweb.param.IdParam;
import com.nutrimedcare.backend.terraweb.param.caregiver.CaregiverListParam;
import com.nutrimedcare.backend.terraweb.param.caregiver.CaregiverSaveParam;
import com.nutrimedcare.backend.terraweb.vo.caregiver.CaregiverDetailVO;
import com.nutrimedcare.backend.terraweb.vo.caregiver.CaregiverListVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/caregiver")
public class CaregiverController {

    @Autowired
    private CaregiverManager caregiverManager;

    @GetMapping("/list")
    public Result<List<CaregiverListVO>> list(@Valid CaregiverListParam param) {
        return Result.success(caregiverManager.list(param));
    }

    @GetMapping("/detail")
    public Result<CaregiverDetailVO> detail(@NotNull(message = "护理员 id 为空") Long id) {
        return Result.success(caregiverManager.detail(id));
    }

    @PostMapping("/save")
    public Result<CaregiverDetailVO> save(@Valid @RequestBody CaregiverSaveParam param) {
        return Result.success(caregiverManager.save(param));
    }

    @PostMapping("/delete")
    public Result<Boolean> delete(@Valid @RequestBody IdParam param) {
        return Result.success(caregiverManager.delete(param));
    }

} 