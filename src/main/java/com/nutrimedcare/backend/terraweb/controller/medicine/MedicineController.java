package com.nutrimedcare.backend.terraweb.controller.medicine;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineImageRecognitionParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineHospitalListVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineImageRecognitionVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/medicine")
public class MedicineController {

    @Autowired
    private MedicineManager medicineManager;

    @Operation(summary = "配药医院列表", description = "配药医院列表")
    @GetMapping("/hospitalList")
    public Result<MedicineHospitalListVO> hospitalList(@Valid @NotNull Long institutionId, String hospitalName) {
        return Result.success(medicineManager.hospitalList(institutionId, hospitalName));
    }

    @Operation(summary = "药品识别结果")
    @PostMapping("/image/recognition")
    public Result<MedicineImageRecognitionVO> imageRecognition(@RequestBody MedicineImageRecognitionParam param) {
        return Result.success(medicineManager.imageRecognition(param));
    }

} 