package com.nutrimedcare.backend.terraweb.controller.medicine;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineInfoManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoBatchParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoList4ResidentParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineSaveParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoSaveVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineListVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/medicine/info")
public class MedicineInfoController {

    @Autowired
    private MedicineInfoManager medicineInfoManager;

    @Operation(summary = "药品保存")
    @PostMapping("/save")
    public Result<MedicineInfoSaveVO> save(@Valid @RequestBody MedicineSaveParam param) {
        return Result.success(medicineInfoManager.save(param));
    }

    @Operation(summary = "药品更新")
    @PostMapping("/update")
    public Result<Boolean> update(@Valid @RequestBody MedicineSaveParam param) {
        return Result.success(medicineInfoManager.update(param));
    }

    @Operation(summary = "药品删除")
    @PostMapping("/delete")
    public Result<Boolean> delete(@Valid @RequestBody MedicineInfoBatchParam param) {
        return Result.success(medicineInfoManager.delete(param));
    }

    @Operation(summary = "药品列表", description = "机构下所有药品")
    @PostMapping("/allList")
    public Result<List<MedicineInfoVO>> allList(@Valid @RequestBody MedicineInfoListParam param) {
        return Result.success(medicineInfoManager.allList(param));
    }

    @Operation(summary = "根据 ids 获取药品列表")
    @PostMapping("/listByIds")
    public Result<List<MedicineInfoVO>> listByIds(@Valid @RequestBody MedicineListParam param) {
        return Result.success(medicineInfoManager.listByIds(param));
    }

    @Operation(summary = "入住人药品列表", description = "入住人下所有药品")
    @GetMapping("/list4Resident")
    public Result<MedicineListVO> list4Resident(@Valid MedicineInfoList4ResidentParam param) {
        return Result.success(medicineInfoManager.list4Resident(param));
    }

    @Operation(summary = "未签名药品列表", description = "入住人下未签名药品列表")
    @GetMapping("/listUnsigned4Resident")
    public Result<MedicineListVO> listUnsigned4Resident(@Valid MedicineInfoList4ResidentParam param) {
        return Result.success(medicineInfoManager.listUnsigned4Resident(param));
    }

} 