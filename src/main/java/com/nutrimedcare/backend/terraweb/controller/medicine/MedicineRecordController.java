package com.nutrimedcare.backend.terraweb.controller.medicine;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineRecordManager;
import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicationRecordCreateParam;
import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicineRecordQueryParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.record.MedicationRecordListVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/medicine/record")
public class MedicineRecordController {

    @Autowired
    private MedicineRecordManager medicineRecordManagers;

    /**
     * 保存服药单
     */
    @PostMapping("/save")
    public Result<Boolean> save(@Valid @RequestBody MedicationRecordCreateParam param) {
        return Result.success(medicineRecordManagers.save(param));
    }

    /**
     * 入住人的服药单详情列表
     */
    @GetMapping("/list")
    public Result<MedicationRecordListVO> list(@Valid MedicineRecordQueryParam param) {
        return Result.success(medicineRecordManagers.list(param));
    }

} 