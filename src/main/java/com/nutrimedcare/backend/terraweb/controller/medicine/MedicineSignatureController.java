package com.nutrimedcare.backend.terraweb.controller.medicine;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineSignatureManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineSignatureListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.signature.MedicineSignatureSaveParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineSignatureVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/medicine/signature")
public class MedicineSignatureController {

    @Autowired
    private MedicineSignatureManager medicineSignatureManager;

    @Operation(summary = "保存签名")
    @PostMapping("/save")
    public Result<Boolean> save(@Valid @RequestBody MedicineSignatureSaveParam param) {
        return Result.success(medicineSignatureManager.save(param));
    }

    @Operation(summary = "最近签名列表")
    @GetMapping("/recentList")
    public Result<List<MedicineSignatureVO>> recentList(@Valid MedicineSignatureListParam param) {
        return Result.success(medicineSignatureManager.recentList(param));
    }

} 