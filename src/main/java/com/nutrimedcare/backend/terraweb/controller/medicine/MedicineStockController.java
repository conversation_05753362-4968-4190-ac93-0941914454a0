package com.nutrimedcare.backend.terraweb.controller.medicine;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineInfoManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoListParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/medicine/stock")
public class MedicineStockController {

    @Autowired
    private MedicineInfoManager medicineInfoManager;

    /**
     * 即将用完的药品列表
     */
    @PostMapping("/runOutSoonList")
    public Result<List<MedicineInfoVO>> runOutSoonList(@Valid @RequestBody MedicineInfoListParam param) {
        return Result.success(medicineInfoManager.runOutSoonList(param));
    }

} 