package com.nutrimedcare.backend.terraweb.controller.nfc;

import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.manager.resident.NfcManager;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcBindParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcCheckInConfirmParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcCheckInParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcTagDataParam;
import com.nutrimedcare.backend.terraweb.vo.nfc.NfcCheckInConfirmDataVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/nfc")
public class NfcController {

    @Autowired
    private NfcManager nfcManager;

    @PostMapping("/bindResident")
    public Result<Boolean> bindResident(@Valid @RequestBody NfcBindParam param) {
        return Result.success(nfcManager.bindResident(param));
    }

    @PostMapping("/rebindResident")
    public Result<Boolean> rebindResident(@Valid @RequestBody NfcBindParam param) {
        return Result.success(nfcManager.rebindResident(param));
    }

    @GetMapping("/checkBindRelation")
    public Result<Boolean> checkBindRelation(@Valid NfcBindParam param) {
        return Result.success(nfcManager.checkBindRelation(param));
    }

    @GetMapping("/checkNfcTagData")
    public Result<Boolean> checkNfcTagData(@Valid NfcTagDataParam param) {
        return Result.success(nfcManager.checkNfcTagData(param));
    }

    @GetMapping("/residentCheckInDataDetail")
    public Result<NfcCheckInConfirmDataVO> residentCheckInDataDetail(@Valid NfcCheckInConfirmParam param) {
        return Result.success(nfcManager.residentCheckInDataDetail(param));
    }

    @PostMapping("/checkIn")
    public Result<Boolean> checkIn(@Valid @RequestBody NfcCheckInParam param) {
        return Result.success(nfcManager.checkIn(param));
    }

} 