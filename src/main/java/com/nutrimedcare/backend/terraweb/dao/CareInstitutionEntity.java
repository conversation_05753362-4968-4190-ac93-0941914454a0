package com.nutrimedcare.backend.terraweb.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import com.nutrimedcare.backend.terraweb.dto.institution.CareInstitutionExtInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_care_institution", autoResultMap = true)
public class CareInstitutionEntity extends BaseEntity {

    private String institutionCode;

    private String institutionName;
    
    private String institutionNameAbbr;

    private String password;

    private String logoUrl;

    private Integer score;

    /**
     * @see com.nutrimedcare.backend.terraweb.dto.institution.CareInstitutionExtInfoDTO
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private CareInstitutionExtInfoDTO extInfo;

} 