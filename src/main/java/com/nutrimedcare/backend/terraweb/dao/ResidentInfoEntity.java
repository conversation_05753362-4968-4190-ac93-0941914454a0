package com.nutrimedcare.backend.terraweb.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("tb_resident_info")
public class ResidentInfoEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long institutionId;

    private String residentName;

    private String residentNamePinyin;

    /*
     * {@link com.nutrimedcare.backend.terraweb.enums.GenderEnum}
     */
    private Integer gender;

    private LocalDateTime birthdate;

    private String locationInfo;

    @TableLogic(value = "0", delval = "unix_timestamp()")
    private Long deleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}