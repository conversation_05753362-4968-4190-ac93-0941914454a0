package com.nutrimedcare.backend.terraweb.dao.caregiver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_care_giver_info")
public class CaregiverInfoEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String displayId;

    private String password;

    private Long institutionId;

    @TableLogic(value = "0", delval = "unix_timestamp()")
    private Long deleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}