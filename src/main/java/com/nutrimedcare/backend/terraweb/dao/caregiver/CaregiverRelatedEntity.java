package com.nutrimedcare.backend.terraweb.dao.caregiver;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_care_giver_related", autoResultMap = true)
public class CaregiverRelatedEntity extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long caregiverId;

    private Long residentId;

    private Long institutionId;

}