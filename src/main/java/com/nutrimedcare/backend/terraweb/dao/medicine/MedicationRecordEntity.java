package com.nutrimedcare.backend.terraweb.dao.medicine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.resolver.CustomTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_medication_record", autoResultMap = true)
public class MedicationRecordEntity extends BaseEntity {

    private Long residentId;

    private Long medicineId;

    private String medicineName;

    @TableField(typeHandler = CustomTypeHandler.class)
    private List<MedicineRecordDetailDTO> usageDetail;

    /**
     * 可用状态
     *
     * @see com.nutrimedcare.backend.terraweb.enums.BooleanEnum
     */
    private Integer validStatus;

} 