package com.nutrimedcare.backend.terraweb.dao.medicine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineUsageTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_medicine_info", autoResultMap = true)
public class MedicineInfoEntity extends BaseEntity {

    private LocalDateTime registerTime;

    private String medicineName;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.MedicinePropertyEnum
     */
    private Integer medicineProperty;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum
     */
    private Integer medicineType;

    private String medicineCode;

    private String batchNumber;

    /**
     * 如 100
     * 搭配单位字段使用，单位为 mg
     */
    private String specification;

    /**
     * mg
     */
    private String specificationUnit;

    private LocalDateTime expirationTime;

    /**
     * 生产厂家
     */
    private String manufacturerInfo;

    /**
     * 配药医院
     */
    private String hospitalInfo;

    private Integer medicineContentPer;

    /**
     * 目前支持 '#'、'ml'
     */
    private String medicineContentPerUnit;

    private Integer medicineQuantity;

    /**
     * 目前支持：盒
     */
    private String medicineQuantityUnit;

    private String dosage;

    /**
     * 目前支持 '#'、'ml'
     */
    private String dosageUnit;

    /**
     * @see MedicineUsageTypeEnum
     */
    private Integer usageInfo;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.BooleanEnum
     */
    private Integer writeOffStatus;

    /**
     * @see com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private MedicineExtInfoDTO extInfo;

    /*
     *
     ************************************************************************
     *                  以下为冗余字段，用于搜索
     */

    private Long institutionId;

    private Long residentId;

    private String residentName;

    private String residentNamePinyin;

    private String locationInfo;

    private String medicineIdentifier;

} 