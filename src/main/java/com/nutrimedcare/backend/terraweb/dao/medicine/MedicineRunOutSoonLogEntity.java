package com.nutrimedcare.backend.terraweb.dao.medicine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRunOutSoonExtInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 药品余量表
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_medicine_run_out_soon_log", autoResultMap = true)
public class MedicineRunOutSoonLogEntity extends BaseEntity {

    private Long institutionId;

    private String locationInfo;

    private String residentName;

    private String residentNamePinyin;

    private String medicineName;

    private String manufacturerInfo;

    /**
     * 剩余数量分数形式
     * 假设 20片，用了 1/4，存储为 19-3-4
     */
    private String medicineStock;

    /**
     * 包括普通用法和特殊用法
     * 以最新的同名药品为准
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private MedicineRunOutSoonExtInfoDTO extInfo;

} 