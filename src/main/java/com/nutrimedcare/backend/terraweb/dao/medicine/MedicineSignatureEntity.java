package com.nutrimedcare.backend.terraweb.dao.medicine;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName("tb_medicine_signature")
public class MedicineSignatureEntity extends BaseEntity {

    private Long institutionId;

    private Long residentId;

    private Long medicineId;

    private String signatureImgUrl;

    /**
     * @see MedicineSignatureRoleTypeEnum
     */
    private Integer roleType;

}