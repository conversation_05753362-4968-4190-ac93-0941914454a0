package com.nutrimedcare.backend.terraweb.dao.medicine;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


/**
 * 药品余量表
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_medicine_stock", autoResultMap = true)
public class MedicineStockEntity extends BaseEntity {

    private Long institutionId;

    private Long residentId;

    private Long medicineId;

    private String medicineName;

    /**
     * 药品总数
     */
    private String totalNum;

    /**
     * 累计使用量
     */
    private String totalUseNum;

    /**
     * 剩余数量分数形式
     * 假设 20片，用了 1/4，存储为 19-3-4
     */
    private String medicineStock;

    /**
     * 每周消耗量（精准）
     */
    private String weeklyConsumeNum;

    /**
     * 每天消耗量（预估）
     */
    private String dailyConsumeNum;


} 