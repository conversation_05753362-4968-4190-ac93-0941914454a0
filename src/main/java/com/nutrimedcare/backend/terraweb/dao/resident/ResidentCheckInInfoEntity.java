package com.nutrimedcare.backend.terraweb.dao.resident;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_resident_check_in_info", autoResultMap = true)
public class ResidentCheckInInfoEntity extends BaseEntity {

    private Long id;

    private LocalDateTime checkInTime;

    private Integer takeTimeType;

    private Long residentId;

    private String locationInfo;

    private String residentName;

    private String residentNamePinyin;

    private String medicineName;

    private Long institutionId;

    private Long caregiverId;

    private String caregiverDisplayId;

    /**
     * 服药状态
     *
     * @see com.nutrimedcare.backend.terraweb.enums.BooleanEnum
     */
    private Integer medicineTakeStatus;
}
