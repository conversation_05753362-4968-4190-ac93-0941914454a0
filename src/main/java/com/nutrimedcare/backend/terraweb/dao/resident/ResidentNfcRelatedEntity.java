package com.nutrimedcare.backend.terraweb.dao.resident;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nutrimedcare.backend.terraweb.dao.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "tb_resident_nfc_related", autoResultMap = true)
public class ResidentNfcRelatedEntity extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * NFC 卡片的 Unique Identifier
     */
    private String nfcUniqueId;

    private Long residentId;

    private Long institutionId;

}