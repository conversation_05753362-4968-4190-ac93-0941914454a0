package com.nutrimedcare.backend.terraweb.dto.institution;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.hutool.core.lang.tuple.Pair;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CareInstitutionExtInfoDTO {

    /**
     * 是否存储楼号信息
     */
    private Integer includeBuildingNum;

    /**
     * 未签名状态下是否可以直接打印药品
     */
    private Integer printWithoutSignature;

    /**
     * 入住人数量限制
     */
    private Integer maxResidentNum = 200;

    /**
     * 服药时间段
     * 比如 1: [0:00, 7:00)
     * 比如 2: [7:00, 9:00)
     *
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum
     */
    private Map<Integer, Pair<String, String>> expirationTimeMap;

}
