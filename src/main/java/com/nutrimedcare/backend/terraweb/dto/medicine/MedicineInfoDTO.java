package com.nutrimedcare.backend.terraweb.dto.medicine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicinePropertyEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineUsageTypeEnum;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class MedicineInfoDTO {

    private Long id;

    private LocalDateTime registerTime;

    private String medicineName;

    /**
     * @see MedicinePropertyEnum
     */
    private Integer medicineProperty;

    /**
     * @see MedicineTypeEnum
     */
    private Integer medicineType;

    private String medicineCode;

    private String batchNumber;

    /**
     * 如 100
     * 搭配单位字段使用，单位为 mg
     */
    private String specification;

    /**
     * mg
     */
    private String specificationUnit;

    private LocalDateTime expirationTime;

    private String manufacturerInfo;

    /**
     * 配药医院信息
     */
    private String hospitalInfo;

    private Integer medicineContentPer;

    private Integer medicineQuantity;

    /**
     * 目前支持：盒
     */
    private String medicineQuantityUnit;

    private String dosage;

    /**
     * @see MedicineUsageTypeEnum
     */
    private Integer usageInfo;

    private Integer writeOffStatus;

    /**
     * @see com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private MedicineExtInfoDTO extInfo;

    private Integer usageIndex;

    /*
     *
     ************************************************************************
     *                  以下为冗余字段，用于搜索
     */

    private Long institutionId;

    private Long residentId;

    private String residentName;

    private String residentNamePinyin;

    private String locationInfo;

    public static MedicineInfoDTO convert2DTO(MedicineInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        MedicineInfoDTO dto = new MedicineInfoDTO();
        BeanUtils.copyProperties(entity, dto);
        dto.setExtInfo(entity.getExtInfo());
        return dto;
    }
}
