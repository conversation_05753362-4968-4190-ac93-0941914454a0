package com.nutrimedcare.backend.terraweb.dto.third;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc <a href="https://apifox.com/apidoc/shared/16404c66-0142-4d88-b9c9-8461882e960d/281546499e0" />
 */
@Data
public class MedicineImageRecognitionResp {

    /**
     * 剂型与剂量
     */
    private String dosageForm;

    /**
     * 使用方法
     */
    private String dosageInstructions;

    /**
     * 医院
     */
    private String dispensingInstitution;

    /**
     * 适应症
     */
    private String indications;

    /**
     * 生产日期
     */
    private String productionDate;

    /**
     * -------------- 以上字段作废 --------------
     */

    /**
     * 药品名称
     */
    private String name;

    /**
     * 生产厂商
     */
    private String manufacturer;

    /**
     * 规格值
     */
    private String specificationValue;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 有效期至
     */
    private String expireDate;

    /**
     * 产品批号
     */
    private String lotNum;

    /**
     * 包装规格
     */
    private String packageSize;

    /**
     * 例如：19.2mg
     */
    private String dose;

    /**
     * 频次
     */
    private String frequency;

}