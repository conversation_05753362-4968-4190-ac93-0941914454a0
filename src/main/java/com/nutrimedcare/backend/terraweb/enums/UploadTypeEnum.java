package com.nutrimedcare.backend.terraweb.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum UploadTypeEnum {
    LOGO(1, "logo/", "机构 logo"),
    SIGNATURE(2, "signature/", "药品确认签字"),
    MEDICINE_RECOGNITION(3, "medicineRecognition/", "药品识别"),
    ;

    private final Integer type;
    private final String prefix;
    private final String desc;

    public static UploadTypeEnum getByType(Integer type) {
        for (UploadTypeEnum uploadType : values()) {
            if (uploadType.getType().equals(type)) {
                return uploadType;
            }
        }
        throw new IllegalArgumentException("未知的上传路径: " + type);
    }

}
