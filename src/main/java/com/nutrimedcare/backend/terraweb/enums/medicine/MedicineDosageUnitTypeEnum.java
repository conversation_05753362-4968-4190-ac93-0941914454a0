package com.nutrimedcare.backend.terraweb.enums.medicine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MedicineDosageUnitTypeEnum {
    SHARP(1, "#"),
    MG(1, "mg"),
    G(2, "g"),
    MICRO_G(3, "μg"),
    IU(5, "IU"),
    ML(8, "mL"),
    PERCENTAGE(7, "%"),
    DI(9, "滴"),
    PIAN(9, "片"),
    LI(9, "粒"),
    WAN(9, "丸"),
    MEI(10, "枚"),
    ;

    private final Integer type;
    private final String desc;

    public static boolean isValid(String desc) {
        return Arrays.stream(MedicineDosageUnitTypeEnum.values())
                .anyMatch(enumValue -> enumValue.getDesc().equals(desc));
    }

    public static Integer convertFromStr(String unit) {
        for (MedicineDosageUnitTypeEnum value : MedicineDosageUnitTypeEnum.values()) {
            if (value.getDesc().equals(unit)) {
                return value.getType();
            }
        }
        return null;
    }

}