package com.nutrimedcare.backend.terraweb.enums.medicine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MedicineExpirationTypeEnum {

    NO_EXPIRATION(1, "新鲜"),
    EXPIRING_SOON(2, "即将过期"),
    EXPIRED(3, "已过期"),
    ;

    private final Integer type;
    private final String desc;

    public static MedicineExpirationTypeEnum getByType(Integer type) {
        for (MedicineExpirationTypeEnum typeEnum : MedicineExpirationTypeEnum.values()) {
            if (Objects.equals(typeEnum.type, type)) {
                return typeEnum;
            }
        }
        return null;
    }

}
