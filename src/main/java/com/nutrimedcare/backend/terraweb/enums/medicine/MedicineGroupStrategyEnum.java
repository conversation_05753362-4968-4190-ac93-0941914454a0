package com.nutrimedcare.backend.terraweb.enums.medicine;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MedicineGroupStrategyEnum {

    NO_GROUP(0, "不聚合"),
    BY_MEDICINE_NAME_AND_REDUCE_STOCK(1, "药品名聚合, 需要累计库存"),
    BY_MEDICINE_NAME_REGISTER_TIME(2, "药品名+注册时间聚合"),
    ;

    private final Integer type;
    private final String desc;

}
