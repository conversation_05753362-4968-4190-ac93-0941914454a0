package com.nutrimedcare.backend.terraweb.enums.medicine;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MedicineSpecificationUnitTypeEnum {
    MG(1, "mg"),
    G(2, "g"),
    MICRO_G(3, "μg"),
    NG(4, "ng"),
    IU(5, "IU"),
    U(6, "U"),
    PERCENTAGE(7, "%"),
    ML(8, "mL"),
    L(9, "L"),
    MICRO_L(10, "μL"),
    ;

    private final Integer type;
    private final String desc;

    public static boolean isValid(String desc) {
        return Arrays.stream(MedicineSpecificationUnitTypeEnum.values())
                .anyMatch(enumValue -> enumValue.getDesc().equals(desc));
    }

    public static Integer convertFromStr(String unit) {
        for (MedicineSpecificationUnitTypeEnum value : MedicineSpecificationUnitTypeEnum.values()) {
            if (value.getDesc().equals(unit)) {
                return value.getType();
            }
        }
        return null;
    }

}