package com.nutrimedcare.backend.terraweb.enums.medicine;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MedicineUsageTypeEnum {
    QD(1, "每日一次"),
    BID(2, "每日二次/每日两次"),
    TID(3, "每日三次"),
    QID(4, "每日四次"),
    QOD(5, "隔日一次"),
    QW(6, "每周一次"),
    BIW(7, "每周两次"),
    Q2H(8, "每两小时一次"),
    Q8H(9, "每八小时一次"),
    N(10, "每晚睡前一次"),
    QAM(11, "每日上午一次"),
    QPM(12, "每日傍晚一次"),
    QNOON(13, "每日中午一次"),
    QH(14, "每小时一次"),
    Q3H(15, "每三小时一次"),
    Q4H(16, "每四小时一次"),
    Q6H(17, "每六小时一次"),
    Q12H(18, "每十二小时一次"),
    TIW(19, "每周三次"),
    Q2W(20, "每两周一次"),
    Q3W(21, "每三周一次"),
    Q1M(22, "每月一次"),
    ;

    private final Integer type;
    private final String desc;

    public static boolean isValid(Integer type) {
        return Arrays.stream(MedicineUsageTypeEnum.values())
                .anyMatch(enumValue -> enumValue.getType().equals(type));
    }

    public static Integer convertFromStr(String frequency) {
        if (StringUtils.isBlank(frequency)) {
            return null;
        }
        for (MedicineUsageTypeEnum value : MedicineUsageTypeEnum.values()) {
            if (value.getDesc().contains(frequency)) {
                return value.getType();
            }
        }
        return null;
    }

}