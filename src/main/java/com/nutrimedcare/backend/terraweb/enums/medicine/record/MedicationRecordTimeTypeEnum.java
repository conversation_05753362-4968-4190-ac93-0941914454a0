package com.nutrimedcare.backend.terraweb.enums.medicine.record;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MedicationRecordTimeTypeEnum {

    BEFORE_BREAKFAST(1, "早餐前"),
    AFTER_BREAKFAST(2, "早餐后"),
    BEFORE_LUNCH(3, "午餐前"),
    AFTER_LUNCH(4, "午餐后"),
    BEFORE_DINNER(5, "晚餐前"),
    AFTER_DINNER(6, "晚餐后"),
    BEFORE_SLEEP(7, "睡前"),
    NO_EAT(-1, "不吃"),
    ;

    private final Integer type;
    private final String desc;

} 