package com.nutrimedcare.backend.terraweb.exception;

import com.nutrimedcare.backend.terraweb.common.BizErrorCode;
import com.nutrimedcare.backend.terraweb.common.ResultCode;
import lombok.Getter;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Getter
public class BusinessException extends RuntimeException {

    private final String code;
    private final String message;

    public BusinessException(String message) {
        super(message);
        this.code = ResultCode.FAILED.getCode();
        this.message = message;
    }

    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    public BusinessException(BizErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }

} 