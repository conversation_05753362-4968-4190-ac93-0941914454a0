package com.nutrimedcare.backend.terraweb.exception;

import com.nutrimedcare.backend.terraweb.common.BizErrorCode;
import com.nutrimedcare.backend.terraweb.common.ResultCode;
import lombok.Getter;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@Getter
public class BusinessException extends RuntimeException {

    private final Integer code;
    private final String bizCode;
    private final String message;

    public BusinessException(String message) {
        super(message);
        this.code = ResultCode.FAILED.getCode();
        this.bizCode = "";
        this.message = message;
    }

    public BusinessException(Integer code, String bizCode, String message) {
        super(message);
        this.code = code;
        this.bizCode = bizCode;
        this.message = message;
    }

    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.bizCode = "";
        this.message = resultCode.getMessage();
    }

    public BusinessException(BizErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = ResultCode.FAILED.getCode();
        this.bizCode = errorCode.getBizCode();
        this.message = errorCode.getMessage();
    }

} 