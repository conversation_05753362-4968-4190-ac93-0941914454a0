package com.nutrimedcare.backend.terraweb.interceptor;

import com.nutrimedcare.backend.terraweb.controller.APIControlController;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * <AUTHOR>
 */
@Component
public class DisableInterceptor implements HandlerInterceptor {

    @Autowired
    private APIControlController apiControlController;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        boolean isEnabled = apiControlController.isEnabled();
        if (!isEnabled) {
            response.setStatus(503);
            response.getWriter().write("Service is temporarily unavailable");
            return false;
        }
        return true;
    }

}