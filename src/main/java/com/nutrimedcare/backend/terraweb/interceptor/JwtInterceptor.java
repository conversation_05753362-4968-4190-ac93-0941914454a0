package com.nutrimedcare.backend.terraweb.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrimedcare.backend.terraweb.common.Result;
import com.nutrimedcare.backend.terraweb.common.ResultCode;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.util.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        token = token.substring(7);
        boolean validateRes = JwtUtil.validateToken(token);
        if (!validateRes) {
            log.info("token expired, token:{}, uri:{}", token, request.getRequestURI());
            setUnauthorizedResponse(response);
            return false;
        }
        return true;
    }

    private void setUnauthorizedResponse(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");

        // 自定义响应体内容
        String json = objectMapper.writeValueAsString(Result.error("401", "token 已过期或无效"));
        response.getWriter().write(json);
    }

} 