package com.nutrimedcare.backend.terraweb.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class LoggingInterceptor implements HandlerInterceptor {

    /**
     * transfer response data
     */
    public static final ThreadLocal<String> RESPONSE_BODY_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * calculate http method time cost
     */
    private static final ThreadLocal<Long> START_TIME_THREAD_LOCAL = new ThreadLocal<>();

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws IOException {
        START_TIME_THREAD_LOCAL.set(System.currentTimeMillis());

        log.info("=== Request Info ===");
        log.info("URI: {}", request.getRequestURI());
        log.info("Method: {}", request.getMethod());
        log.info("Headers: {}", objectMapper.writeValueAsString(getHeaders(request)));

        // fot GET Method
        if (!"GET".equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        Enumeration<String> parameterNames = request.getParameterNames();
        Map<String, String> parameterMap = new HashMap<>();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            parameterMap.put(paramName, request.getParameter(paramName));
        }
        log.info("Params: {}", objectMapper.writeValueAsString(parameterMap));
        return true;
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        return headers;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, HttpServletResponse response, @NonNull Object handler, Exception ex) {
        long duration = System.currentTimeMillis() - START_TIME_THREAD_LOCAL.get();
        START_TIME_THREAD_LOCAL.remove();

        log.info("=== Response Info ===");
        log.info("HttpStatus: {}", response.getStatus());
        log.info("ResponseData: {}", RESPONSE_BODY_THREAD_LOCAL.get());
        log.info("cost: {} ms", duration);
        RESPONSE_BODY_THREAD_LOCAL.remove();
    }

}