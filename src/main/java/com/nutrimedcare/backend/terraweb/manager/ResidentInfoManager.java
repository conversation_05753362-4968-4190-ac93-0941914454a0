package com.nutrimedcare.backend.terraweb.manager;

import com.nutrimedcare.backend.terraweb.param.IdParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentCheckInDataListParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentListParam;
import com.nutrimedcare.backend.terraweb.param.resident.ResidentSaveParam;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentCheckIntInfoVO;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentInfoVO;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface ResidentInfoManager {

    ResidentInfoVO detail(Long id, Integer medicineType);

    List<ResidentInfoVO> list(ResidentListParam param);

    List<ResidentInfoVO> simpleList(ResidentListParam param);

    List<ResidentInfoVO> searchRes(ResidentListParam param);

    ResidentInfoVO save(ResidentSaveParam param);

    Boolean delete(IdParam param);

    /**
     * 查询入住人列表，包含标签绑定信息
     */
    List<ResidentInfoVO> list4EaseTab(ResidentListParam param);

    ResidentInfoVO detail4Ease(Long id);

    List<ResidentCheckIntInfoVO> listCheckInInfo(ResidentCheckInDataListParam param);

}