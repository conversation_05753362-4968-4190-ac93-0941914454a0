package com.nutrimedcare.backend.terraweb.manager.caregiver;

import com.nutrimedcare.backend.terraweb.param.IdParam;
import com.nutrimedcare.backend.terraweb.param.caregiver.CaregiverListParam;
import com.nutrimedcare.backend.terraweb.param.caregiver.CaregiverSaveParam;
import com.nutrimedcare.backend.terraweb.vo.caregiver.CaregiverDetailVO;
import com.nutrimedcare.backend.terraweb.vo.caregiver.CaregiverListVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CaregiverManager {

    List<CaregiverListVO> list(CaregiverListParam param);

    CaregiverDetailVO detail(Long id);

    CaregiverDetailVO save(CaregiverSaveParam param);

    Boolean delete(IdParam param);

}
