package com.nutrimedcare.backend.terraweb.manager.caregiver.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverRelatedEntity;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.caregiver.CaregiverManager;
import com.nutrimedcare.backend.terraweb.param.IdParam;
import com.nutrimedcare.backend.terraweb.param.caregiver.CaregiverListParam;
import com.nutrimedcare.backend.terraweb.param.caregiver.CaregiverSaveParam;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverRelatedService;
import com.nutrimedcare.backend.terraweb.util.EncryptUtil;
import com.nutrimedcare.backend.terraweb.vo.caregiver.CaregiverDetailVO;
import com.nutrimedcare.backend.terraweb.vo.caregiver.CaregiverListVO;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CaregiverManagerImpl implements CaregiverManager {

    @Autowired
    private EncryptUtil encryptUtil;

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private CaregiverInfoService caregiverInfoService;

    @Autowired
    private CaregiverRelatedService caregiverRelatedService;

    @Override
    public List<CaregiverListVO> list(CaregiverListParam param) {
        if (param == null || param.getInstitutionId() == null) {
            return Collections.emptyList();
        }

        List<CaregiverInfoEntity> list = caregiverInfoService.list(new LambdaQueryWrapper<CaregiverInfoEntity>()
                .eq(CaregiverInfoEntity::getInstitutionId, param.getInstitutionId())
                .like(param.getDisplayId() != null, CaregiverInfoEntity::getDisplayId, param.getDisplayId())
                .orderByAsc(CaregiverInfoEntity::getDisplayId)
        );
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(CaregiverListVO::convert2VO).filter(Objects::nonNull).toList();
    }

    @Override
    public CaregiverDetailVO detail(Long id) {
        if (id == null) {
            return null;
        }

        CaregiverInfoEntity entity = caregiverInfoService.getById(id);
        if (entity == null) {
            throw new BusinessException("该护理员不存在");
        }
        CaregiverDetailVO vo = CaregiverDetailVO.convert2VO(entity);

        fillRelatedResidentList(vo);

        return vo;
    }

    private void fillRelatedResidentList(CaregiverDetailVO vo) {
        if (vo == null || vo.getId() == null) {
            return;
        }

        List<CaregiverRelatedEntity> relatedList = listRelatedResidentIds(vo.getId());

        List<Long> residentIds = Optional.of(relatedList).orElse(Collections.emptyList())
                .stream().map(CaregiverRelatedEntity::getResidentId).filter(Objects::nonNull).toList();
        List<ResidentInfoVO> residentVOList = listResidentInfoVO(residentIds);

        vo.setRelatedResidentList(residentVOList);
    }

    private List<CaregiverRelatedEntity> listRelatedResidentIds(Long caregiverId) {
        if (caregiverId == null) {
            return Collections.emptyList();
        }
        return caregiverRelatedService.list(new LambdaQueryWrapper<CaregiverRelatedEntity>()
                .eq(CaregiverRelatedEntity::getCaregiverId, caregiverId)
        );
    }

    private List<ResidentInfoVO> listResidentInfoVO(List<Long> residentIds) {
        if (CollUtil.isEmpty(residentIds)) {
            return Collections.emptyList();
        }
        List<ResidentInfoEntity> residentInfoList = residentInfoService.list(new LambdaQueryWrapper<ResidentInfoEntity>()
                .in(ResidentInfoEntity::getId, residentIds)
                .orderByAsc(ResidentInfoEntity::getLocationInfo)
        );
        return residentInfoList.stream().map(ResidentInfoVO::convert2VO).filter(Objects::nonNull).toList();
    }

    @Override
    public CaregiverDetailVO save(CaregiverSaveParam param) {
        if (param == null) {
            return null;
        }

        // check
        checkPasswordLength(param.getPassword());
        checkCaregiverDisplayIdUnique(param.getInstitutionId(), param.getId(), param.getDisplayId());

        // 检查当前角色是否有权限添加护理员

        // save caregiver
        CaregiverInfoEntity entity = buildCaregiverInfoEntity(param);
        boolean saveRes = caregiverInfoService.saveOrUpdate(entity);
        if (!saveRes) {
            log.warn("caregiver manager, fail to save / update caregiver, param:{}", param);
            return null;
        }

        List<Long> newRelatedResidentIds = CollUtil.isEmpty(param.getRelatedResidentIds()) ? Collections.emptyList() : param.getRelatedResidentIds();

        List<CaregiverRelatedEntity> caregiverRelatedList = listRelatedResidentIds(entity.getId());
        List<Long> existedRelatedResidentIds = caregiverRelatedList.stream().map(CaregiverRelatedEntity::getResidentId).toList();

        // 处理关联的入住人
        addRelatedResident(entity.getId(), newRelatedResidentIds, existedRelatedResidentIds, param.getInstitutionId());
        delUnrelatedResident(entity.getId(), newRelatedResidentIds, existedRelatedResidentIds);

        return CaregiverDetailVO.convert2VO(entity);
    }

    private void checkPasswordLength(String password) {
        String decryptedPassword = encryptUtil.aesDecrypt(password);
        if (decryptedPassword.length() < 8 || decryptedPassword.length() > 50) {
            throw new BusinessException("护理员密码长度错误");
        }
    }

    private void checkCaregiverDisplayIdUnique(Long institutionId, Long caregiverId, String displayId) {
        if (institutionId == null || StringUtils.isBlank(displayId)) {
            return;
        }
        List<CaregiverInfoEntity> list = caregiverInfoService.list(new LambdaQueryWrapper<CaregiverInfoEntity>()
//                .eq(CaregiverInfoEntity::getInstitutionId, institutionId)
                        .eq(CaregiverInfoEntity::getDisplayId, displayId)
        );

        if (CollUtil.isEmpty(list)) {
            return;
        }

        CaregiverInfoEntity caregiverInfo = list.get(0);
        if (caregiverId == null || !Objects.equals(caregiverInfo.getId(), caregiverId)) {
            throw new BusinessException("该账号 ID 已存在");
        }

    }

    private CaregiverInfoEntity buildCaregiverInfoEntity(CaregiverSaveParam param) {
        CaregiverInfoEntity entity = new CaregiverInfoEntity();
        BeanUtils.copyProperties(param, entity);
        return entity;
    }

    private void delUnrelatedResident(Long caregiverId, List<Long> newRelatedResidentIds, List<Long> existedRelatedResidentIds) {
        Collection<Long> needDelList = CollUtil.subtract(existedRelatedResidentIds, newRelatedResidentIds);
        if (CollUtil.isEmpty(needDelList)) {
            return;
        }
        caregiverRelatedService.remove(new LambdaQueryWrapper<CaregiverRelatedEntity>()
                .eq(CaregiverRelatedEntity::getCaregiverId, caregiverId)
                .in(CaregiverRelatedEntity::getResidentId, needDelList)
        );
    }

    private void addRelatedResident(Long caregiverId, List<Long> newRelatedResidentIds, List<Long> existedRelatedResidentIds, Long institutionId) {
        Collection<Long> needInsertList = CollUtil.subtract(newRelatedResidentIds, existedRelatedResidentIds);
        if (CollUtil.isEmpty(needInsertList)) {
            return;
        }

        List<CaregiverRelatedEntity> insertList = needInsertList.stream().map(residentId -> buildCaregiverRelatedEntity(caregiverId, residentId, institutionId)).toList();
        caregiverRelatedService.saveBatch(insertList);
    }

    private CaregiverRelatedEntity buildCaregiverRelatedEntity(Long caregiverId, Long residentId, Long institutionId) {
        CaregiverRelatedEntity entity = new CaregiverRelatedEntity();
        entity.setCaregiverId(caregiverId);
        entity.setResidentId(residentId);
        entity.setInstitutionId(institutionId);
        return entity;
    }

    @Override
    public Boolean delete(IdParam param) {
        if (param == null || param.getId() == null) {
            return false;
        }

        boolean removeRes = caregiverInfoService.removeById(param.getId());
        if (!removeRes) {
            log.warn("caregiver manager, fail to delete caregiver, id: {}", param.getId());
            return false;
        }

        boolean removeRelationsRes = caregiverRelatedService.remove(new LambdaQueryWrapper<CaregiverRelatedEntity>()
                .eq(CaregiverRelatedEntity::getCaregiverId, param.getId())
        );
        if (!removeRelationsRes) {
            log.warn("caregiver manager, fail to delete  relations, id: {}", param.getId());
            return false;
        }

        return true;
    }

}
