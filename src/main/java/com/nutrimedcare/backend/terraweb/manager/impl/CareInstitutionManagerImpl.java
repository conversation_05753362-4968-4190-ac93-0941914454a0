package com.nutrimedcare.backend.terraweb.manager.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineRunOutSoonLogEntity;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.CareInstitutionManager;
import com.nutrimedcare.backend.terraweb.param.insitution.InstitutionSaveParam;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineRunOutSoonLogService;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import com.nutrimedcare.backend.terraweb.vo.institution.InstitutionDetailVO;
import com.nutrimedcare.backend.terraweb.vo.institution.InstitutionRelatedVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Component
public class CareInstitutionManagerImpl implements CareInstitutionManager {

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicineRunOutSoonLogService medicineRunOutSoonLogService;

    @Override
    public InstitutionDetailVO detail(Long id) {
        // get institution detail
        CareInstitutionEntity entity = careInstitutionService.getById(id);
        if (entity == null) {
            throw new BusinessException("机构信息不存在");
        }

        // count resident num
        long residentNum = residentInfoService.count(new LambdaQueryWrapper<ResidentInfoEntity>()
                .eq(ResidentInfoEntity::getInstitutionId, entity.getId())
        );

        // count runOutSoon Num
        long runOutSoonNum = medicineRunOutSoonLogService.count(new LambdaQueryWrapper<MedicineRunOutSoonLogEntity>()
                .eq(MedicineRunOutSoonLogEntity::getInstitutionId, entity.getId())
                .gt(MedicineRunOutSoonLogEntity::getCreateTime, LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
        );

        // count medicine num
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = LocalDateTimeUtil.endOfDay(LocalDateTime.now(), true);

        long totalNum = medicineInfoService.count(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getInstitutionId, entity.getId())
        );

        long expiredNum = medicineInfoService.count(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getInstitutionId, entity.getId())
                .eq(MedicineInfoEntity::getWriteOffStatus, 0)
                .eq(MedicineInfoEntity::getExpirationTime, startOfDay)
        );
        long expiringSoonNum = medicineInfoService.count(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getInstitutionId, entity.getId())
                .eq(MedicineInfoEntity::getWriteOffStatus, 0)
                .between(MedicineInfoEntity::getExpirationTime, startOfDay.plusMinutes(1), endOfDay.plusDays(179))
        );
        long noExpirationNum = medicineInfoService.count(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getInstitutionId, entity.getId())
                .eq(MedicineInfoEntity::getWriteOffStatus, 0)
                .ge(MedicineInfoEntity::getExpirationTime, startOfDay.plusDays(180))
        );


        return InstitutionDetailVO.convertToVO(
                entity,
                InstitutionRelatedVO.builder()
                        .residentNum((int) residentNum)
                        .totalNum((int) totalNum)
                        .noExpirationNum((int) noExpirationNum)
                        .expiringSoonNum((int) expiringSoonNum)
                        .expiredNum((int) expiredNum)
                        .runOutSoonNum((int) runOutSoonNum)
                        .build()
        );
    }

    /*
     ******************************************************************
     */

    @Override
    public Boolean insert(InstitutionSaveParam param) {
        CareInstitutionEntity entity = new CareInstitutionEntity();
        BeanUtils.copyProperties(param, entity);
        entity.setPassword(BCrypt.hashpw(param.getPassword(), BCrypt.gensalt()));
        return careInstitutionService.save(entity);
    }

    @Override
    public Boolean update(InstitutionSaveParam param) {
        CareInstitutionEntity entity = new CareInstitutionEntity();
        BeanUtils.copyProperties(param, entity);
        entity.setPassword(null);
        return careInstitutionService.updateById(entity);
    }

} 