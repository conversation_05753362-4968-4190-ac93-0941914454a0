package com.nutrimedcare.backend.terraweb.manager.impl;

import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverInfoEntity;
import com.nutrimedcare.backend.terraweb.enums.AppTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.UserRoleTypeEnum;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.LoginManager;
import com.nutrimedcare.backend.terraweb.param.login.Login4EaseParam;
import com.nutrimedcare.backend.terraweb.param.login.LoginParam;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverInfoService;
import com.nutrimedcare.backend.terraweb.util.EncryptUtil;
import com.nutrimedcare.backend.terraweb.util.JwtUtil;
import com.nutrimedcare.backend.terraweb.vo.login.LoginVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class LoginManagerImpl implements LoginManager {

    @Autowired
    private EncryptUtil encryptUtil;

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private CaregiverInfoService caregiverInfoService;

    @Override
    public LoginVO login(LoginParam request) {
        if (request == null) {
            return null;
        }

        // find institution
        CareInstitutionEntity institution = careInstitutionService.getOne(
                new LambdaQueryWrapper<CareInstitutionEntity>().eq(CareInstitutionEntity::getInstitutionCode, request.getInstitutionCode())
        );
        if (institution == null) {
            throw new BusinessException("该机构不存在");
        }

        //  aes decrypt & check password
        String decryptedPassword = encryptUtil.aesDecrypt(request.getPassword());
        if (!BCrypt.checkpw(decryptedPassword, institution.getPassword())) {
            throw new BusinessException("请输入正确的密码");
        }

        return LoginVO.builder()
                .token(JwtUtil.generateToken(institution.getInstitutionName(), institution.getInstitutionCode()))
                .institutionId(institution.getId())
                .build();
    }

    @Override
    public LoginVO login4Ease(Login4EaseParam request) {
        if (request == null || request.getUsername() == null || request.getPassword() == null) {
            return null;
        }

        // 护理员登录
        LoginVO caregiverLoginVO = caregiverLogin(request.getUsername(), request.getPassword());
        if (caregiverLoginVO != null) {
            return caregiverLoginVO;
        }

        // 管理员登录
        String decryptedPassword = encryptUtil.aesDecrypt(request.getPassword());
        LoginVO adminLoginVO = institutionLogin(request.getUsername(), decryptedPassword);
        if (adminLoginVO != null) {
            return adminLoginVO;
        }

        throw new BusinessException("请检查账户名称");
    }

    private LoginVO caregiverLogin(String username, String password) {
        CaregiverInfoEntity entity = caregiverInfoService.getOne(new LambdaQueryWrapper<CaregiverInfoEntity>()
                .eq(CaregiverInfoEntity::getDisplayId, username)
        );
        if (entity == null) {
            return null;
        }

        if (!Objects.equals(entity.getPassword(), password)) {
            throw new BusinessException("请输入正确密码");
        }

        return LoginVO.builder()
                .token(JwtUtil.generateToken(
                        String.valueOf(entity.getId()),
                        UserRoleTypeEnum.EASE_APP_CAREGIVER.getType().toString(),
                        AppTypeEnum.NUTRIMED_EASE.getType().toString()
                ))
                .institutionId(entity.getInstitutionId())
                .bizId(entity.getId())
                .appType(AppTypeEnum.NUTRIMED_EASE.getType())
                .easeRoleType(UserRoleTypeEnum.EASE_APP_CAREGIVER.getType())
                .build();
    }

    private LoginVO institutionLogin(String username, String decryptedPassword) {
        CareInstitutionEntity entity = careInstitutionService.getOne(
                new LambdaQueryWrapper<CareInstitutionEntity>().eq(CareInstitutionEntity::getInstitutionCode, username)
        );
        if (entity == null) {
            return null;
        }
        checkPasswordValid(decryptedPassword, entity.getPassword());

        return LoginVO.builder()
                .token(JwtUtil.generateToken(
                        String.valueOf(entity.getId()),
                        UserRoleTypeEnum.EASE_APP_ADMIN.getType().toString(),
                        AppTypeEnum.NUTRIMED_EASE.getType().toString()
                ))
                .institutionId(entity.getId())
                .bizId(entity.getId())
                .appType(AppTypeEnum.NUTRIMED_EASE.getType())
                .easeRoleType(UserRoleTypeEnum.EASE_APP_ADMIN.getType())
                .build();
    }

    private void checkPasswordValid(String decryptedPassword, String passwordInDb) {
        if (!BCrypt.checkpw(decryptedPassword, passwordInDb)) {
            throw new BusinessException("请输入正确密码");
        }
    }

}
