package com.nutrimedcare.backend.terraweb.manager.medicine;

import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoBatchParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoList4ResidentParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineInfoListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineSaveParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoSaveVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineListVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MedicineInfoManager {

    MedicineInfoSaveVO save(MedicineSaveParam param);

    Boolean update(MedicineSaveParam param);

    Boolean delete(MedicineInfoBatchParam param);

    List<MedicineInfoVO> allList(MedicineInfoListParam param);

    List<MedicineInfoVO> listByIds(MedicineListParam param);

    MedicineListVO list4Resident(MedicineInfoList4ResidentParam param);

    MedicineListVO listUnsigned4Resident(MedicineInfoList4ResidentParam param);

    List<MedicineInfoVO> runOutSoonList(MedicineInfoListParam param);

    Map<Long, List<MedicineInfoEntity>> listSignedMedicineInfoByResidentId(List<Long> residentIdList);
}
