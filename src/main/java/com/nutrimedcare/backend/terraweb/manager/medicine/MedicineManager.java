package com.nutrimedcare.backend.terraweb.manager.medicine;

import com.nutrimedcare.backend.terraweb.param.medicine.MedicineImageRecognitionParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineHospitalListVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineImageRecognitionVO;

/**
 * <AUTHOR>
 */
public interface MedicineManager {

    MedicineHospitalListVO hospitalList(Long institutionId, String hospitalName);

    MedicineImageRecognitionVO imageRecognition(MedicineImageRecognitionParam param);

}