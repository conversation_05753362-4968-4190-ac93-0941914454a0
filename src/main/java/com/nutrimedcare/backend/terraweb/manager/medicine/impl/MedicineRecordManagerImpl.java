package com.nutrimedcare.backend.terraweb.manager.medicine.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineSpecificationDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineUsageDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineRecordManager;
import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicationRecordCreateParam;
import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicationRecordDetailParam;
import com.nutrimedcare.backend.terraweb.param.medicine.record.MedicineRecordQueryParam;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineSignatureService;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import com.nutrimedcare.backend.terraweb.util.FractionUtil;
import com.nutrimedcare.backend.terraweb.util.UsageInfoUtil;
import com.nutrimedcare.backend.terraweb.vo.medicine.record.MedicationRecordDetailVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.record.MedicationRecordListVO;
import com.nutrimedcare.backend.terraweb.vo.medicine.record.MedicationRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.dromara.hutool.core.math.Fraction;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MedicineRecordManagerImpl implements MedicineRecordManager {

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Autowired
    private MedicineSignatureService medicineSignatureService;

    @Autowired
    private MedicineStockService medicineStockService;

    private ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    public Boolean save(MedicationRecordCreateParam param) {
        if (param == null || CollUtil.isEmpty(param.getList())) {
            return false;
        }

        // 保存
        List<MedicationRecordEntity> saveList = convert2SaveList(param);
        boolean saveRes = medicationRecordService.saveOrUpdateBatch(saveList);
        if (!saveRes) {
            log.warn("medicine record save, failed to save, param: {}", param);
            return false;
        }

        // 保存完后删除入住人的其他服药单信息
        List<Long> usefulIds = saveList.stream().map(MedicationRecordEntity::getId).toList();
        medicationRecordService.remove(new LambdaQueryWrapper<MedicationRecordEntity>()
                .eq(MedicationRecordEntity::getResidentId, param.getResidentId())
                .notIn(MedicationRecordEntity::getId, usefulIds)
        );

        // 需要重新计算药物库存
        List<Long> updateMedicineIds = param.getList().stream()
//                .filter(detailParam -> Objects.isNull(detailParam.getId()))
                .map(MedicationRecordDetailParam::getMedicineId)
                .toList();

        executorService.submit(() -> calMedicineStock(param.getResidentId(), updateMedicineIds));

        return true;
    }

    public void calMedicineStock(Long residentId, List<Long> updateMedicineIds) {
        log.info("medicine record save, add medicine stock, residentId:{}, updateMedicineIds:{}", residentId, updateMedicineIds);
        if (CollUtil.isEmpty(updateMedicineIds)) {
            return;
        }

        // 查询药品信息
        List<MedicineInfoEntity> medicineInfoList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .in(MedicineInfoEntity::getId, updateMedicineIds)
        );
        if (CollUtil.isEmpty(medicineInfoList)) {
            log.warn("medicine record save, add medicine stock, medicineInfoList is empty");
            return;
        }

        Set<String> medicineNames = medicineInfoList.stream().map(MedicineInfoEntity::getMedicineName).collect(Collectors.toSet());
        Map<String, MedicineInfoEntity> medicineInfoMap = medicineInfoList.stream()
                .collect(Collectors.toMap(MedicineInfoEntity::getMedicineName, Function.identity(), (v1, v2) -> v2));

        // 查询出同名药的老库存信息
        List<MedicineStockEntity> oldStockInfoList = medicineStockService.list(new LambdaQueryWrapper<MedicineStockEntity>()
                .eq(MedicineStockEntity::getResidentId, residentId)
                .in(MedicineStockEntity::getMedicineName, medicineNames)
        );
        Map<String, MedicineStockEntity> oldStockInfoMap = oldStockInfoList.stream().collect(Collectors.toMap(MedicineStockEntity::getMedicineName, Function.identity(), (v1, v2) -> v2));

        // 删除同名药的老库存信息
        medicineStockService.remove(new LambdaQueryWrapper<MedicineStockEntity>()
                .eq(MedicineStockEntity::getResidentId, residentId)
                .in(MedicineStockEntity::getMedicineName, medicineNames)
        );

        // 重新计算库存信息

        // 当药品对应的 medicine record 存在 usageInfo 时，则使用 record 中存在的 usageInfo 参与消耗量的计算
        Map<String, MedicationRecordEntity> medicineRecordMap = findMedicineRecordMap(residentId, updateMedicineIds);

        List<MedicineStockEntity> list = medicineNames.stream().map(medicineName -> {

            MedicineInfoEntity medicineInfoEntity = medicineInfoMap.get(medicineName);
            MedicationRecordEntity medicationRecord = medicineRecordMap.get(medicineName);

            // 用于计算库存
            List<MedicineInfoEntity> medicineList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                    .eq(MedicineInfoEntity::getResidentId, residentId)
                    .eq(MedicineInfoEntity::getMedicineName, medicineName)
                    .eq(MedicineInfoEntity::getWriteOffStatus, BooleanEnum.FALSE.getType())
                    .orderByDesc(MedicineInfoEntity::getId)
            );
            if (CollUtil.isEmpty(medicineList)) {
                return null;
            }
            return buildMedicineStockEntity(residentId, medicineInfoEntity, medicationRecord, medicineList, oldStockInfoMap.get(medicineList.get(0).getMedicineName()));
        }).filter(Objects::nonNull).toList();
        medicineStockService.saveBatch(list);
    }

    private Map<String, MedicationRecordEntity> findMedicineRecordMap(Long residentId, List<Long> updateMedicineIds) {
        if (CollUtil.isEmpty(updateMedicineIds)) {
            return Collections.emptyMap();
        }
        List<MedicationRecordEntity> medicineRecordList = medicationRecordService.list(
                new LambdaQueryWrapper<MedicationRecordEntity>()
                        .eq(MedicationRecordEntity::getResidentId, residentId)
                        .in(MedicationRecordEntity::getMedicineId, updateMedicineIds)
                        .eq(MedicationRecordEntity::getValidStatus, BooleanEnum.TRUE.getType())
        );
        if (CollUtil.isEmpty(medicineRecordList)) {
            return Collections.emptyMap();
        }
        return medicineRecordList.stream()
                .collect(Collectors.toMap(MedicationRecordEntity::getMedicineName, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public void calMedicineStock(Long residentId, Set<String> medicineNames) {
        log.info("medicine record save, add medicine stock, residentId:{}, medicineNames:{}", residentId, medicineNames);
        if (CollUtil.isEmpty(medicineNames)) {
            return;
        }

        // 查询出同名药的老库存信息
        List<MedicineStockEntity> oldStockInfoList = medicineStockService.list(new LambdaQueryWrapper<MedicineStockEntity>()
                .eq(MedicineStockEntity::getResidentId, residentId)
                .in(MedicineStockEntity::getMedicineName, medicineNames)
        );
        Map<String, MedicineStockEntity> oldStockInfoMap = oldStockInfoList.stream().collect(Collectors.toMap(MedicineStockEntity::getMedicineName, Function.identity(), (v1, v2) -> v2));

        // 删除同名药的老库存信息
        medicineStockService.remove(new LambdaQueryWrapper<MedicineStockEntity>()
                .eq(MedicineStockEntity::getResidentId, residentId)
                .in(MedicineStockEntity::getMedicineName, medicineNames)
        );

        // 重新计算库存信息
        List<MedicineStockEntity> list = medicineNames.stream().map(medicineName -> {
            List<MedicineInfoEntity> medicineList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                    .eq(MedicineInfoEntity::getResidentId, residentId)
                    .eq(MedicineInfoEntity::getMedicineName, medicineName)
                    .eq(MedicineInfoEntity::getWriteOffStatus, BooleanEnum.FALSE.getType())
                    .orderByDesc(MedicineInfoEntity::getId)
            );
            if (CollUtil.isEmpty(medicineList)) {
                return null;
            }
            return buildMedicineStockEntity(residentId, medicineList.get(0), null,
                    medicineList, oldStockInfoMap.get(medicineList.get(0).getMedicineName()));
        }).filter(Objects::nonNull).toList();
        medicineStockService.saveBatch(list);
    }

    private MedicineStockEntity buildMedicineStockEntity(Long residentId, MedicineInfoEntity queryLastMedicine, MedicationRecordEntity queryMedicationRecord,
                                                         List<MedicineInfoEntity> medicineList, MedicineStockEntity oldStockEntity) {
        MedicineStockEntity stockEntity = new MedicineStockEntity();
        stockEntity.setInstitutionId(queryLastMedicine.getInstitutionId());
        stockEntity.setResidentId(residentId);
        stockEntity.setMedicineName(queryLastMedicine.getMedicineName());
        stockEntity.setMedicineId(queryLastMedicine.getId());
        stockEntity.setTotalNum(calTotalNum(medicineList) + "-0-0");
        stockEntity.setMedicineStock(FractionUtil.convertFromFraction(calTotalStock(medicineList)));
        stockEntity.setTotalUseNum("0-0-0");
        stockEntity.setWeeklyConsumeNum(queryMedicationRecord == null ?
                oldStockEntity.getWeeklyConsumeNum() : FractionUtil.convertFromFraction(UsageInfoUtil.calWeekConsumeNum(queryLastMedicine, queryMedicationRecord))
        );
        stockEntity.setDailyConsumeNum(queryMedicationRecord == null ?
                oldStockEntity.getDailyConsumeNum() : FractionUtil.convertFromFraction(UsageInfoUtil.calDailyConsumeNum(queryLastMedicine, queryMedicationRecord, Collections.emptyMap()))
        );

        fillMedicineStock(stockEntity, oldStockEntity);

        return stockEntity;
    }

    private void fillMedicineStock(MedicineStockEntity stockEntity, MedicineStockEntity oldStockEntity) {
        if (oldStockEntity == null) {
            return;
        }
        // 使用量直接继承
        stockEntity.setTotalUseNum(oldStockEntity.getTotalUseNum());
        // 计算库存， = 总数 - 使用量
        Fraction stockFraction = FractionUtil.convert2Fraction(stockEntity.getTotalNum())
                .subtract(FractionUtil.convert2Fraction(stockEntity.getTotalUseNum()));
        stockEntity.setMedicineStock(FractionUtil.convertFromFraction(stockFraction));
    }

    private int calTotalNum(List<MedicineInfoEntity> medicineInfoList) {
        return medicineInfoList.stream()
                .map(MedicineInfoEntity::getExtInfo)
                .filter(Objects::nonNull)
                .map(MedicineExtInfoDTO::getInitNum)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
    }

    private Fraction calTotalStock(List<MedicineInfoEntity> medicineInfoList) {
        return medicineInfoList.stream()
                .filter(entity -> !BooleanEnum.TRUE.getType().equals(entity.getWriteOffStatus()))
                .map(MedicineInfoEntity::getExtInfo)
                .filter(Objects::nonNull)
                .map(MedicineExtInfoDTO::getStock)
                .filter(Objects::nonNull)
                .map(FractionUtil::convert2Fraction)
                .reduce(Fraction.ZERO, Fraction::add);
    }

    private List<MedicationRecordEntity> convert2SaveList(MedicationRecordCreateParam param) {
        return param.getList().stream()
                .map(info -> {
                    if (CollUtil.isEmpty(info.getUsageDetail())) {
                        return null;
                    }
                    MedicationRecordEntity entity = new MedicationRecordEntity();
                    BeanUtils.copyProperties(info, entity);
                    entity.setResidentId(param.getResidentId());

                    // 设置用法详情，并为每个详情项设置快照信息
                    List<MedicineRecordDetailDTO> usageDetailWithSnap = setUsageInfoSnap(info.getUsageDetail(), info.getMedicineId());
                    entity.setUsageDetail(usageDetailWithSnap);
                    entity.setValidStatus(BooleanEnum.TRUE.getType());
                    return entity;
                }).filter(Objects::nonNull).toList();
    }

    private List<MedicineRecordDetailDTO> setUsageInfoSnap(List<MedicineRecordDetailDTO> usageDetail, Long medicineId) {
        if (CollUtil.isEmpty(usageDetail) || medicineId == null) {
            return usageDetail;
        }

        // 查询药品信息，获取当前的用法用量
        MedicineInfoEntity medicineInfo = medicineInfoService.getById(medicineId);
        if (medicineInfo == null) {
            return usageDetail;
        }

        // 构建药品的用法信息列表
        List<MedicineUsageDTO> medicineUsageList = new ArrayList<>();

        // 添加主用法信息
        MedicineUsageDTO mainUsage = new MedicineUsageDTO();
        mainUsage.setUsageType(medicineInfo.getUsageInfo());
        mainUsage.setDosageInfo(medicineInfo.getDosage());
        mainUsage.setDosageUnit(medicineInfo.getDosageUnit());
        medicineUsageList.add(mainUsage);

        // 添加特殊用法信息
        if (medicineInfo.getExtInfo() != null && CollUtil.isNotEmpty(medicineInfo.getExtInfo().getSpecialUsageList())) {
            medicineUsageList.addAll(medicineInfo.getExtInfo().getSpecialUsageList());
        }

        // 为每个服药单详情项设置对应的快照信息
        for (int i = 0; i < usageDetail.size() && i < medicineUsageList.size(); i++) {
            MedicineRecordDetailDTO detail = usageDetail.get(i);
            MedicineUsageDTO snapUsage = medicineUsageList.get(i);

            // 创建快照信息
            MedicineUsageDTO usageInfoSnap = new MedicineUsageDTO();
            usageInfoSnap.setUsageType(snapUsage.getUsageType());
            usageInfoSnap.setDosageInfo(snapUsage.getDosageInfo());
            usageInfoSnap.setDosageUnit(snapUsage.getDosageUnit());

            detail.setUsageInfoSnap(usageInfoSnap);
        }

        return usageDetail;
    }

    @Override
    public MedicationRecordListVO list(MedicineRecordQueryParam param) {
        if (param == null || param.getResidentId() == null) {
            return MedicationRecordListVO.defaultVO();
        }

        //  获取入住人的所有服药单记录
        List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                .eq(MedicationRecordEntity::getResidentId, param.getResidentId())
                .orderByAsc(MedicationRecordEntity::getId)
        );

        // 获取相同用法条件下最早的药品
        Map<String, MedicineInfoEntity> earlySameUsageMedicineInfoMap = findEarlySameUsageMedicineInfoMap(param.getResidentId(), recordList)
                .stream().collect(Collectors.toMap(MedicineInfoEntity::getMedicineName, Function.identity(), (v1, v2) -> v2));
        // 根据服药单信息展示信息
        List<MedicationRecordVO> showList = buildRecordShowList(recordList, earlySameUsageMedicineInfoMap);

        // 包括用户所有已经签名的药物信息，如存在有效的服药单记录，则填充 id和具体服药时间
        List<MedicationRecordVO> editList = buildRecordEditList(param, showList, earlySameUsageMedicineInfoMap);

        List<MedicationRecordVO> sortedEditList = editList.stream()
                .sorted(Comparator.comparing(MedicationRecordVO::getMedicineId))
                .toList();

        return MedicationRecordListVO.builder()
                .showList(showList)
                .editList(sortedEditList)
                .build();
    }

    private List<MedicationRecordVO> buildRecordShowList(List<MedicationRecordEntity> recordList, Map<String, MedicineInfoEntity> medicineInfoMap) {
        if (CollUtil.isEmpty(recordList)) {
            return Collections.emptyList();
        }

        return recordList.stream().map(recordEntity -> {
            MedicineInfoEntity medicineInfoEntity = medicineInfoMap.get(recordEntity.getMedicineName());
            if (medicineInfoEntity == null) {
                return null;
            }
            MedicationRecordVO recordVO = MedicationRecordVO.convert2VO(recordEntity, getDosageList(medicineInfoEntity));
            // 填充药品信息
            fillMedicineInfo(recordVO, medicineInfoEntity);
            return recordVO;
        }).filter(Objects::nonNull).toList();
    }

    private List<MedicineInfoEntity> getInvalidMedicineInfoList(Long residentId, List<MedicationRecordEntity> recordList) {
        // 失效的服药单直接根据药品 id 查询药品信息
        List<Long> invalidMedicineIds = recordList.stream().filter(recordEntity -> BooleanEnum.FALSE.getType().equals(recordEntity.getValidStatus())).map(MedicationRecordEntity::getMedicineId).toList();
        if (CollUtil.isEmpty(invalidMedicineIds)) {
            return Collections.emptyList();
        }
        // 获取药品列表
        return medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getResidentId, residentId)
                .ge(MedicineInfoEntity::getExpirationTime, LocalDateTime.now().toLocalDate().atStartOfDay())
                .in(MedicineInfoEntity::getId, invalidMedicineIds)
        );
    }

    private List<MedicineInfoEntity> getValidMedicineInfoList(Long residentId, List<MedicationRecordEntity> recordList) {
        List<String> validMedicineNames = recordList.stream()
                .filter(recordEntity -> BooleanEnum.TRUE.getType().equals(recordEntity.getValidStatus())).map(MedicationRecordEntity::getMedicineName)
                .toList();
        if (CollUtil.isEmpty(validMedicineNames)) {
            return Collections.emptyList();
        }

        // 查询最旧的未核销药品
        List<MedicineInfoEntity> medicineList = medicineInfoService.findOldestSameNameMedicine(residentId, validMedicineNames, 0);
        if (CollUtil.isEmpty(medicineList)) {
            return medicineInfoService.findLastSameNameMedicine(residentId, validMedicineNames, null);
        }

        // list 中药名对应信息是否为空，如果为空，则查询最近的药品信息
        List<String> queryMedicineNames = medicineList.stream().map(MedicineInfoEntity::getMedicineName).toList();

        List<String> filterMedicineNames = validMedicineNames.stream().filter(name -> !queryMedicineNames.contains(name)).toList();
        List<MedicineInfoEntity> filterList = medicineInfoService.findLastSameNameMedicine(residentId, filterMedicineNames, null);

        return Stream.of(medicineList, filterList).flatMap(Collection::stream).toList();
    }

    private List<MedicineInfoEntity> findEarlySameUsageMedicineInfoMap(Long residentId, List<MedicationRecordEntity> recordList) {
        if (CollUtil.isEmpty(recordList)) {
            return Collections.emptyList();
        }

        List<MedicineInfoEntity> result = new ArrayList<>();
        for (MedicationRecordEntity recordEntity : recordList) {
            // 查询该药品名称的所有候选药品（按时间降序，最新的在前面）
            List<MedicineInfoEntity> candidates = medicineInfoService.list(
                    new LambdaQueryWrapper<MedicineInfoEntity>()
                            .eq(MedicineInfoEntity::getResidentId, residentId)
                            .eq(MedicineInfoEntity::getMedicineName, recordEntity.getMedicineName())
                            .orderByDesc(MedicineInfoEntity::getUpdateTime)
            );

            // 找到最后一个用法匹配的药品（即最早的满足条件的药品）
            MedicineInfoEntity matchedMedicine = findLastMatchedMedicine(recordEntity.getUsageDetail(), candidates);

            if (matchedMedicine != null) {
                result.add(matchedMedicine);
            } else {
                // 如果没有找到匹配的，降级使用最老的药品
                if (CollUtil.isNotEmpty(candidates)) {
                    result.add(candidates.get(candidates.size() - 1));
                }
            }
        }

        return result;
    }

    private boolean isUsageOrderMatched(List<MedicineRecordDetailDTO> usageDetail, MedicineInfoEntity medicineEntity) {
        // 提取服药单的用法信息列表（包含用法类型、剂量信息、剂量单位）
        List<MedicineUsageDTO> recordUsageList = usageDetail.stream()
                .map(detail -> {
                    // 优先使用实际用法用量信息，如果没有则使用快照信息
                    MedicineUsageDTO usageInfo = detail.getUsageInfoSnap();

                    // 如果都没有，则根据 usageType 构建基础用法信息
                    if (usageInfo == null) {
                        usageInfo = new MedicineUsageDTO();
                        usageInfo.setUsageType(detail.getUsageType());
                    }

                    return usageInfo;
                })
                .toList();

        // 构建药品的用法信息列表
        List<MedicineUsageDTO> medicineUsageList = new ArrayList<>();

        // 添加主用法信息
        MedicineUsageDTO mainUsage = new MedicineUsageDTO();
        mainUsage.setUsageType(medicineEntity.getUsageInfo());
        mainUsage.setDosageInfo(medicineEntity.getDosage());
        mainUsage.setDosageUnit(medicineEntity.getDosageUnit());
        medicineUsageList.add(mainUsage);

        // 添加特殊用法信息
        if (medicineEntity.getExtInfo() != null && CollUtil.isNotEmpty(medicineEntity.getExtInfo().getSpecialUsageList())) {
            medicineUsageList.addAll(medicineEntity.getExtInfo().getSpecialUsageList());
        }

        // 比较两个用法信息列表是否完全匹配
        if (recordUsageList.size() != medicineUsageList.size()) {
            return false;
        }

        for (int i = 0; i < recordUsageList.size(); i++) {
            MedicineUsageDTO recordUsage = recordUsageList.get(i);
            MedicineUsageDTO medicineUsage = medicineUsageList.get(i);

            if (!isUsageDTOEqual(recordUsage, medicineUsage)) {
                return false;
            }
        }

        return true;
    }

    private MedicineInfoEntity findLastMatchedMedicine(List<MedicineRecordDetailDTO> usageDetail, List<MedicineInfoEntity> candidates) {
        if (CollUtil.isEmpty(candidates)) {
            return null;
        }

        List<MedicineInfoEntity> filterList = candidates.stream()
                .filter(entity -> Objects.equals(entity.getWriteOffStatus(), BooleanEnum.FALSE.getType()))
                .toList();

        if (CollUtil.isEmpty(filterList)) {
            return null;
        }

        MedicineInfoEntity lastMatched = null;
        boolean foundFirstMatch = false;

        for (MedicineInfoEntity candidate : filterList) {
            if (isUsageOrderMatched(usageDetail, candidate)) {
                foundFirstMatch = true;
                lastMatched = candidate;
            } else if (foundFirstMatch) {
                // 已经找到过匹配的药品，现在遇到不匹配的，停止搜索
                break;
            }
            // 如果还没找到第一个匹配的药品，继续遍历
        }

        return lastMatched;
    }

    private List<Pair<String, String>> getDosageList(MedicineInfoEntity entity) {
        if (entity == null) {
            return Collections.emptyList();
        }

        List<Pair<String, String>> dosageList = new ArrayList<>();
        // 普通用法
        dosageList.add(Pair.of(entity.getDosage(), entity.getDosageUnit()));

        if (entity.getExtInfo() == null || CollUtil.isEmpty(entity.getExtInfo().getSpecialUsageList())) {
            return dosageList;
        }
        entity.getExtInfo().getSpecialUsageList()
                .forEach(usageInfo -> dosageList.add(Pair.of(usageInfo.getDosageInfo(), usageInfo.getDosageUnit())));

        return dosageList;
    }

    private void fillMedicineInfo(MedicationRecordVO vo, MedicineInfoEntity medicineInfoEntity) {
        if (vo == null || medicineInfoEntity == null) {
            return;
        }
        vo.setResidentName(medicineInfoEntity.getResidentName());
        vo.setLocationInfo(medicineInfoEntity.getLocationInfo());
        vo.setSpecificationInfo(buildSpecificationInfoStr(medicineInfoEntity));
        vo.setSpecificationInfoList(buildSpecificationInfo(medicineInfoEntity));
    }

    private List<MedicationRecordVO> buildRecordEditList(MedicineRecordQueryParam param, List<MedicationRecordVO> showList,
                                                         Map<String, MedicineInfoEntity> earlySameUsageMedicineInfoMap) {
        List<MedicationRecordVO> validList = showList.stream().filter(vo -> BooleanEnum.TRUE.getType().equals(vo.getValidStatus())).toList();
        Map<String, MedicationRecordVO> validMap = validList.stream().collect(Collectors.toMap(
                MedicationRecordVO::getMedicineName,
                Function.identity(),
                (v1, v2) -> v2
        ));

        List<MedicineInfoEntity> medicineInfoList = listSignedMedicine(param.getResidentId());

        return medicineInfoList.stream().map(medicineInfoEntity -> {
            if (!validMap.containsKey(medicineInfoEntity.getMedicineName())) {
                MedicineInfoEntity earlySameUsageMedicineInfo = findEarlySameUsageMedicineInfo(medicineInfoEntity);
                MedicationRecordVO vo = new MedicationRecordVO();
                vo.setMedicineId(medicineInfoEntity.getId());
                vo.setMedicineName(medicineInfoEntity.getMedicineName());
                vo.setSpecificationInfo(buildSpecificationInfoStr(earlySameUsageMedicineInfo));
                vo.setSpecificationInfoList(buildSpecificationInfo(earlySameUsageMedicineInfo));
                vo.setRecordList(convert2UsageList(medicineInfoEntity));
                return vo;
            }
            return validMap.get(medicineInfoEntity.getMedicineName());
        }).filter(Objects::nonNull).toList();
    }

    private MedicineInfoEntity findEarlySameUsageMedicineInfo(MedicineInfoEntity medicineInfoEntity) {
        if (medicineInfoEntity == null || medicineInfoEntity.getResidentId() == null ||
                StringUtils.isBlank(medicineInfoEntity.getMedicineName())) {
            return null;
        }

        // 构建目标药品的用法信息列表（包含用法类型、剂量信息、剂量单位）
        List<MedicineUsageDTO> targetUsageList = new ArrayList<>();

        // 添加主用法信息
        MedicineUsageDTO mainUsage = new MedicineUsageDTO();
        mainUsage.setUsageType(medicineInfoEntity.getUsageInfo());
        mainUsage.setDosageInfo(medicineInfoEntity.getDosage());
        mainUsage.setDosageUnit(medicineInfoEntity.getDosageUnit());
        targetUsageList.add(mainUsage);

        // 添加特殊用法信息
        if (medicineInfoEntity.getExtInfo() != null && CollUtil.isNotEmpty(medicineInfoEntity.getExtInfo().getSpecialUsageList())) {
            targetUsageList.addAll(medicineInfoEntity.getExtInfo().getSpecialUsageList());
        }

        // 查询该药品名称的所有候选药品（按时间降序，最新的在前面）
        List<MedicineInfoEntity> candidates = medicineInfoService.list(
                new LambdaQueryWrapper<MedicineInfoEntity>()
                        .eq(MedicineInfoEntity::getResidentId, medicineInfoEntity.getResidentId())
                        .eq(MedicineInfoEntity::getMedicineName, medicineInfoEntity.getMedicineName())
                        .eq(MedicineInfoEntity::getWriteOffStatus, BooleanEnum.FALSE.getType())
                        .orderByDesc(MedicineInfoEntity::getUpdateTime)
        );

        if (CollUtil.isEmpty(candidates)) {
            return null;
        }

        // 找到最后一个用法匹配的药品（即最早的满足条件的药品）
        MedicineInfoEntity lastMatched = null;
        boolean foundFirstMatch = false;

        for (MedicineInfoEntity candidate : candidates) {
            if (isUsageInfoMatchedByEntity(targetUsageList, candidate)) {
                foundFirstMatch = true;
                lastMatched = candidate;
            } else if (foundFirstMatch) {
                // 已经找到过匹配的药品，现在遇到不匹配的，停止搜索
                break;
            }
            // 如果还没找到第一个匹配的药品，继续遍历
        }

        // 如果没有找到匹配的，降级使用最老的药品
        if (lastMatched == null && CollUtil.isNotEmpty(candidates)) {
            lastMatched = candidates.get(candidates.size() - 1);
        }

        return lastMatched;
    }

    /**
     * 判断药品实体的用法信息是否匹配（包括用法类型、剂量信息、剂量单位）
     */
    private boolean isUsageInfoMatchedByEntity(List<MedicineUsageDTO> targetUsageList, MedicineInfoEntity medicineEntity) {
        // 构建候选药品的用法信息列表
        List<MedicineUsageDTO> candidateUsageList = new ArrayList<>();

        // 添加主用法信息
        MedicineUsageDTO mainUsage = new MedicineUsageDTO();
        mainUsage.setUsageType(medicineEntity.getUsageInfo());
        mainUsage.setDosageInfo(medicineEntity.getDosage());
        mainUsage.setDosageUnit(medicineEntity.getDosageUnit());
        candidateUsageList.add(mainUsage);

        // 添加特殊用法信息
        if (medicineEntity.getExtInfo() != null && CollUtil.isNotEmpty(medicineEntity.getExtInfo().getSpecialUsageList())) {
            candidateUsageList.addAll(medicineEntity.getExtInfo().getSpecialUsageList());
        }

        // 比较两个用法信息列表是否完全匹配
        if (targetUsageList.size() != candidateUsageList.size()) {
            return false;
        }

        for (int i = 0; i < targetUsageList.size(); i++) {
            MedicineUsageDTO target = targetUsageList.get(i);
            MedicineUsageDTO candidate = candidateUsageList.get(i);

            if (!isUsageDTOEqual(target, candidate)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 比较两个 MedicineUsageDTO 是否相等（用法类型、剂量信息、剂量单位）
     */
    private boolean isUsageDTOEqual(MedicineUsageDTO usage1, MedicineUsageDTO usage2) {
        if (usage1 == null && usage2 == null) {
            return true;
        }
        if (usage1 == null || usage2 == null) {
            return false;
        }

        return Objects.equals(usage1.getUsageType(), usage2.getUsageType()) &&
                Objects.equals(usage1.getDosageInfo(), usage2.getDosageInfo()) &&
                Objects.equals(usage1.getDosageUnit(), usage2.getDosageUnit());
    }

    private String buildSpecificationInfoStr(MedicineInfoEntity medicineInfoEntity) {
        if (medicineInfoEntity == null) {
            return "";
        }
        String baseInfo = medicineInfoEntity.getSpecification() + medicineInfoEntity.getSpecificationUnit();
        if (medicineInfoEntity.getExtInfo() == null || CollUtil.isEmpty(medicineInfoEntity.getExtInfo().getSpecificationList())) {
            return baseInfo;
        }
        List<MedicineSpecificationDTO> specificationList = medicineInfoEntity.getExtInfo().getSpecificationList();
        String specificationListStr = specificationList.stream().map(info -> info.getSpecificationValue() + info.getSpecificationUnit()).collect(Collectors.joining(":"));
        return baseInfo + ":" + specificationListStr;
    }

    private List<MedicineSpecificationDTO> buildSpecificationInfo(MedicineInfoEntity medicineInfoEntity) {
        if (medicineInfoEntity == null) {
            return Collections.emptyList();
        }
        MedicineSpecificationDTO baseInfo = builMedicineSpecificationDTO(medicineInfoEntity.getSpecification(), medicineInfoEntity.getSpecificationUnit());
        if (medicineInfoEntity.getExtInfo() == null || CollUtil.isEmpty(medicineInfoEntity.getExtInfo().getSpecificationList())) {
            return Collections.singletonList(baseInfo);
        }
        List<MedicineSpecificationDTO> extSpecificationList = medicineInfoEntity.getExtInfo().getSpecificationList();
        return Stream.of(Collections.singletonList(baseInfo), extSpecificationList).flatMap(Collection::stream).toList();
    }

    private MedicineSpecificationDTO builMedicineSpecificationDTO(String specificationValue, String specificationUnit) {
        MedicineSpecificationDTO dto = new MedicineSpecificationDTO();
        dto.setSpecificationValue(specificationValue);
        dto.setSpecificationUnit(specificationUnit);
        return dto;
    }

    private List<MedicationRecordDetailVO> convert2UsageList(MedicineInfoEntity entity) {
        if (entity == null) {
            return Collections.emptyList();
        }

        List<MedicineUsageDTO> specialUsageList = entity.getExtInfo().getSpecialUsageList();
        if (CollUtil.isEmpty(specialUsageList)) {
            return Stream.of(Collections.singleton(convert2RecordDetail(entity.getUsageInfo(), entity.getDosage(), entity.getDosageUnit()))).flatMap(Collection::stream).toList();
        }

        List<MedicationRecordDetailVO> otherRcordList = specialUsageList.stream()
                .map(usageDTO -> convert2RecordDetail(usageDTO.getUsageType(), usageDTO.getDosageInfo(), usageDTO.getDosageUnit()))
                .toList();
        return Stream.of(Collections.singleton(convert2RecordDetail(entity.getUsageInfo(), entity.getDosage(), entity.getDosageUnit())), otherRcordList).flatMap(Collection::stream).toList();
    }

    private MedicationRecordDetailVO convert2RecordDetail(Integer usageType, String dosageInfo, String dosageUnit) {
        MedicationRecordDetailVO vo = new MedicationRecordDetailVO();
        vo.setUsageType(usageType);
        vo.setDosageInfo(dosageInfo);
        vo.setDosageUnit(dosageUnit);
        return vo;
    }

    private List<MedicineInfoEntity> listSignedMedicine(Long residentId) {
        if (residentId == null) {
            return Collections.emptyList();
        }
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();

        // 获取入住人的所有药品, 倒序排列（为了获取最新的药品信息）
        List<MedicineInfoEntity> originList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getResidentId, residentId)
                .eq(MedicineInfoEntity::getWriteOffStatus, BooleanEnum.FALSE.getType())
                .ge(MedicineInfoEntity::getExpirationTime, startOfDay)
                .orderByDesc(MedicineInfoEntity::getId)
        );

        // 过滤未签名药品
        List<MedicineInfoEntity> filterMedicineList = filterUnsignedMedicines(residentId, originList);

        // 获取同名的第一个药品
        Map<String, MedicineInfoEntity> groupedMap = new LinkedHashMap<>();
        for (MedicineInfoEntity medicine : filterMedicineList) {
            if (!groupedMap.containsKey(medicine.getMedicineName())) {
                groupedMap.put(medicine.getMedicineName(), medicine);
            }
        }
        return groupedMap.values().stream().toList();
    }

    private List<MedicineInfoEntity> filterUnsignedMedicines(Long residentId, List<MedicineInfoEntity> medicineInfoList) {
        if (CollUtil.isEmpty(medicineInfoList)) {
            return Collections.emptyList();
        }

        // list medicine signature info
        List<Long> allMedicineIds = medicineInfoList.stream().map(MedicineInfoEntity::getId).toList();
        List<MedicineSignatureEntity> existingSignatures = medicineSignatureService.list(new LambdaQueryWrapper<MedicineSignatureEntity>()
                .eq(MedicineSignatureEntity::getResidentId, residentId)
                .in(MedicineSignatureEntity::getMedicineId, allMedicineIds)
        );

        // group by medicineId
        Map<Long, List<MedicineSignatureEntity>> signaturesByMedicine = existingSignatures.stream().collect(
                Collectors.groupingBy(MedicineSignatureEntity::getMedicineId)
        );

        // filter unsigned
        return medicineInfoList.stream().filter(medicine -> {
            List<MedicineSignatureEntity> signatures = signaturesByMedicine.getOrDefault(medicine.getId(), Collections.emptyList());
            Set<Integer> roleTypes = signatures.stream()
                    .map(MedicineSignatureEntity::getRoleType)
                    .collect(Collectors.toSet());
            if (MedicineTypeEnum.DISPENSING_OF_MEDICINES.getType().equals(medicine.getMedicineType())) {
                return roleTypes.containsAll(Arrays.asList(
                        MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                        MedicineSignatureRoleTypeEnum.PHARMACY.getType()
                ));
            }
            return roleTypes.containsAll(Arrays.asList(
                    MedicineSignatureRoleTypeEnum.FAMILY_MEMBER.getType(),
                    MedicineSignatureRoleTypeEnum.MANAGER.getType(),
                    MedicineSignatureRoleTypeEnum.PHARMACY.getType()
            ));
        }).toList();
    }

}