package com.nutrimedcare.backend.terraweb.manager.medicine.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineUsageDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineRecordManager;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineSignatureManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineSignatureListParam;
import com.nutrimedcare.backend.terraweb.param.medicine.signature.MedicineSignatureParam;
import com.nutrimedcare.backend.terraweb.param.medicine.signature.MedicineSignatureSaveParam;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineSignatureService;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineSignatureVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MedicineSignatureManagerImpl implements MedicineSignatureManager {

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicineSignatureService medicineSignatureService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Autowired
    private MedicineRecordManager medicineRecordManager;

    @Override
    public Boolean save(MedicineSignatureSaveParam param) {
        if (param == null || CollUtil.isEmpty(param.getSignatureList())) {
            return false;
        }

        List<MedicineSignatureParam> signatureList = param.getSignatureList();
        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getResidentId());

        // check signature list size
        checkSignatureListSize(param.getMedicineType(), param.getSignType(), param.getSignatureList());

        // only save pharmacy sign
        if (MedicineSignTypeEnum.ONLY_MANUFACTURER.getType().equals(param.getSignType())) {
            return onlySavePharmacySign(param, residentInfo, signatureList);
        }

        // 是否签完所有的签名
        Set<Integer> roleSet = param.getSignatureList().stream().map(MedicineSignatureParam::getRoleType).collect(Collectors.toSet());
        boolean hasFullSign = roleSet.contains(MedicineSignatureRoleTypeEnum.PHARMACY.getType());

        // from insert
        if (CollUtil.isNotEmpty(param.getMedicineIds())) {
            List<MedicineSignatureEntity> signatureEntityList = param.getMedicineIds().stream()
                    .flatMap(medicineId -> convert2SignatureList(residentInfo, medicineId, signatureList, Collections.emptyMap()).stream())
                    .toList();

            boolean saveRes = medicineSignatureService.saveBatch(signatureEntityList);
            if (!saveRes) {
                log.warn("save signature failed, param:{}", param);
                return false;
            }

            // 签名保存完成后，如存在同药名的服药单，则将其失效
            dealSameNameMedicineRecord(param.getResidentId(), param.getMedicineIds(), hasFullSign);

            return true;
        }

        // 查询出 resident 下所有未签名的药品(只能更新未签名药品)
        Map<Long, Set<Integer>> unsignedMedicineMap = getUnsignedMedicineMap(param.getMedicineType(), param.getMedicineProperty(), residentInfo.getId());
        // build signature save list
        List<MedicineSignatureEntity> signatureEntityList = unsignedMedicineMap.keySet().stream()
                .flatMap(medicineId -> convert2SignatureList(residentInfo, medicineId, signatureList, unsignedMedicineMap).stream())
                .toList();

        dealSameNameMedicineRecord(param.getResidentId(), new ArrayList<>(unsignedMedicineMap.keySet()), hasFullSign);

        return medicineSignatureService.saveBatch(signatureEntityList);
    }

    private void dealSameNameMedicineRecord(Long residentId, List<Long> medicineIds, boolean hasFullSign) {
        if (CollUtil.isEmpty(medicineIds) || !hasFullSign) {
            return;
        }
        List<MedicineInfoEntity> medicineInfoList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .in(MedicineInfoEntity::getId, medicineIds)
        );
        List<String> medicineNames = medicineInfoList.stream().map(MedicineInfoEntity::getMedicineName).toList();
        Map<String, MedicineInfoEntity> medicineMap = medicineInfoList.stream().collect(Collectors.toMap(MedicineInfoEntity::getMedicineName, Function.identity(), (v1, v2) -> v2));

        // 根据药名查询服药单记录对应的药品信息
        Map<String, MedicineInfoEntity> existedMedicineInfoMap = findExistedMedicineInfoMap(residentId, medicineNames);
        // 如果新增药品的用法与服药单中的药品用法是一致的，则不更新服药单
        List<String> filterMedicineNames = medicineNames.stream().filter(medicineName -> !judgeSameUsage(medicineMap.get(medicineName), existedMedicineInfoMap.get(medicineName))).toList();

        // 更新有变化的服药单
        invalidMedicationRecord(residentId, filterMedicineNames);

        // 重新计算库存
        List<String> existMedicineNames = medicineNames.stream().filter(medicineName -> judgeSameUsage(medicineMap.get(medicineName), existedMedicineInfoMap.get(medicineName))).toList();
        medicineRecordManager.calMedicineStock(residentId, new HashSet<>(existMedicineNames));
    }

    private boolean judgeSameUsage(MedicineInfoEntity oldEntity, MedicineInfoEntity newEntity) {
        if (oldEntity == null || newEntity == null) {
            return false;
        }

        // 判断普通用法
        if (!Objects.equals(oldEntity.getUsageInfo(), newEntity.getUsageInfo())
                || !Objects.equals(oldEntity.getDosage(), newEntity.getDosage())
                || !Objects.equals(oldEntity.getDosageUnit(), newEntity.getDosageUnit())) {
            return false;
        }

        // 判断特殊用法
        List<MedicineUsageDTO> oldSpecialUsageList = oldEntity.getExtInfo().getSpecialUsageList();
        List<MedicineUsageDTO> newSpecialUsageList = newEntity.getExtInfo().getSpecialUsageList();
        if (!Objects.equals(oldSpecialUsageList.size(), newSpecialUsageList.size())) {
            return false;
        }

        if (oldSpecialUsageList.isEmpty()) {
            return true;
        }

        Set<MedicineUsageDTO> oldSet = new HashSet<>(oldSpecialUsageList);
        Set<MedicineUsageDTO> newSet = new HashSet<>(newSpecialUsageList);

        return oldSet.equals(newSet);
    }

    private Map<String, MedicineInfoEntity> findExistedMedicineInfoMap(Long residentId, List<String> medicineNames) {
        if (residentId == null || CollUtil.isEmpty(medicineNames)) {
            return Collections.emptyMap();
        }
        List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                .eq(MedicationRecordEntity::getResidentId, residentId)
                .in(MedicationRecordEntity::getMedicineName, medicineNames)
                .eq(MedicationRecordEntity::getValidStatus, BooleanEnum.TRUE.getType())
        );
        if (CollUtil.isEmpty(recordList)) {
            return Collections.emptyMap();
        }
        List<Long> existedMedicineIds = recordList.stream().map(MedicationRecordEntity::getMedicineId).toList();
        List<MedicineInfoEntity> medicineInfoList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .in(MedicineInfoEntity::getId, existedMedicineIds)
        );
        return medicineInfoList.stream().collect(Collectors.toMap(
                MedicineInfoEntity::getMedicineName,
                Function.identity(),
                (v1, v2) -> v2
        ));
    }

    private void invalidMedicationRecord(Long residentId, List<String> medicineNames) {
        if (residentId == null || CollUtil.isEmpty(medicineNames)) {
            return;
        }
        medicationRecordService.update(new LambdaUpdateWrapper<MedicationRecordEntity>()
                .set(MedicationRecordEntity::getValidStatus, BooleanEnum.FALSE.getType())
                .eq(MedicationRecordEntity::getResidentId, residentId)
                .in(MedicationRecordEntity::getMedicineName, medicineNames)
                .eq(MedicationRecordEntity::getValidStatus, BooleanEnum.TRUE.getType())
        );
    }

    private boolean onlySavePharmacySign(MedicineSignatureSaveParam param, ResidentInfoEntity residentInfo, List<MedicineSignatureParam> signatureList) {
        List<MedicineSignatureParam> pharmacySignlist = signatureList.stream()
                .filter(signature -> MedicineSignatureRoleTypeEnum.PHARMACY.getType().equals(signature.getRoleType()))
                .toList();

        Set<Long> unsignedMedicineSet = listMedicineWithoutPharmacySign(param.getMedicineType(), param.getMedicineProperty(), residentInfo.getId());
        if (CollUtil.isEmpty(unsignedMedicineSet)) {
            return true;
        }

        List<MedicineSignatureEntity> updateList = unsignedMedicineSet.stream().map(medicineId -> {
            MedicineSignatureEntity signatureEntity = new MedicineSignatureEntity();
            signatureEntity.setInstitutionId(residentInfo.getInstitutionId());
            signatureEntity.setResidentId(residentInfo.getId());
            signatureEntity.setMedicineId(medicineId);
            signatureEntity.setSignatureImgUrl(pharmacySignlist.get(0).getSignatureImgUrl());
            signatureEntity.setRoleType(MedicineSignatureRoleTypeEnum.PHARMACY.getType());
            return signatureEntity;
        }).toList();

        return medicineSignatureService.saveBatch(updateList);
    }

    private void checkSignatureListSize(Integer medicineType, Integer signType, List<MedicineSignatureParam> signatureList) {
        if (CollUtil.isEmpty(signatureList)) {
            return;
        }

        Set<Integer> roleTypeSet = signatureList.stream().map(MedicineSignatureParam::getRoleType).collect(Collectors.toSet());

        // 校验药房签名
        if (MedicineSignTypeEnum.ONLY_MANUFACTURER.getType().equals(signType)) {
            checkPharmacySign(roleTypeSet);
            return;
        }

        // 普通签名，存在 2 个、3个的情况（配药医院可不签名）
        if (MedicineTypeEnum.MEDICINES_FAMILY_MEMBERS_BRING.getType().equals(medicineType)
                && !roleTypeSet.containsAll(Set.of(MedicineSignatureRoleTypeEnum.FAMILY_MEMBER.getType(), MedicineSignatureRoleTypeEnum.MANAGER.getType()))) {
            throw new BusinessException("需要家属、经办人签名");
        }

        // 代配药不需要家属签名
        if (MedicineTypeEnum.DISPENSING_OF_MEDICINES.getType().equals(medicineType)
                && !roleTypeSet.contains(MedicineSignatureRoleTypeEnum.MANAGER.getType())) {
            throw new BusinessException("需要经办人签名");
        }

    }

    private void checkPharmacySign(Set<Integer> roleTypeSet) {
        if (!roleTypeSet.contains(MedicineSignatureRoleTypeEnum.PHARMACY.getType())) {
            throw new BusinessException("未检查到药房签名");
        }
    }

    private Map<Long, Set<Integer>> getUnsignedMedicineMap(Integer medicineType, Integer medicineProperty, Long residentId) {
        // list all medicine
        List<MedicineInfoEntity> allMedicines = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getResidentId, residentId)
                .eq(MedicineInfoEntity::getMedicineType, medicineType)
                .eq(MedicineInfoEntity::getMedicineProperty, medicineProperty)
        );
        List<Long> allMedicineIds = Optional.ofNullable(allMedicines).orElse(Collections.emptyList()).stream().map(MedicineInfoEntity::getId).toList();

        // list medicine signature info
        List<MedicineSignatureEntity> existingSignatures = medicineSignatureService.list(new LambdaQueryWrapper<MedicineSignatureEntity>()
                .eq(MedicineSignatureEntity::getResidentId, residentId)
                .in(MedicineSignatureEntity::getMedicineId, allMedicineIds)
        );
        Map<Long, List<MedicineSignatureEntity>> signaturesByMedicine = existingSignatures.stream().collect(
                Collectors.groupingBy(MedicineSignatureEntity::getMedicineId)
        );

        Map<Long, Set<Integer>> map = new HashMap<>();

        // filter unsigned
        Optional.ofNullable(allMedicines)
                .orElse(Collections.emptyList())
                .forEach(medicine -> {
                    List<MedicineSignatureEntity> signatures = signaturesByMedicine.getOrDefault(medicine.getId(), Collections.emptyList());
                    if (signatures.size() != 3) {
                        map.put(medicine.getId(), signatures.stream().map(MedicineSignatureEntity::getRoleType).collect(Collectors.toSet()));
                        return;
                    }
                    Set<Integer> roleTypes = signatures.stream()
                            .map(MedicineSignatureEntity::getRoleType)
                            .collect(Collectors.toSet());
                    if (roleTypes.size() != 3) {
                        map.put(medicine.getId(), signatures.stream().map(MedicineSignatureEntity::getRoleType).collect(Collectors.toSet()));
                    }
                });

        return map;
    }

    private Set<Long> listMedicineWithoutPharmacySign(Integer medicineType, Integer medicineProperty, Long residentId) {
        // list all medicine
        List<MedicineInfoEntity> allMedicines = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                .eq(MedicineInfoEntity::getResidentId, residentId)
                .eq(MedicineInfoEntity::getMedicineType, medicineType)
                .eq(MedicineInfoEntity::getMedicineProperty, medicineProperty)
        );
        List<Long> allMedicineIds = Optional.ofNullable(allMedicines).orElse(Collections.emptyList()).stream().map(MedicineInfoEntity::getId).toList();

        // list medicine signature info
        List<MedicineSignatureEntity> existingSignatures = medicineSignatureService.list(new LambdaQueryWrapper<MedicineSignatureEntity>()
                .eq(MedicineSignatureEntity::getResidentId, residentId)
                .in(MedicineSignatureEntity::getMedicineId, allMedicineIds)
        );
        Map<Long, List<MedicineSignatureEntity>> signaturesByMedicine = existingSignatures.stream().collect(
                Collectors.groupingBy(MedicineSignatureEntity::getMedicineId)
        );

        Set<Long> idSet = new HashSet<>();

        // filter unsigned
        Optional.ofNullable(allMedicines)
                .orElse(Collections.emptyList())
                .forEach(medicine -> {
                    List<MedicineSignatureEntity> signatures = signaturesByMedicine.getOrDefault(medicine.getId(), Collections.emptyList());
                    Set<Integer> roleTypes = signatures.stream()
                            .map(MedicineSignatureEntity::getRoleType)
                            .collect(Collectors.toSet());
                    if (roleTypes.contains(MedicineSignatureRoleTypeEnum.MANAGER.getType()) &&
                            !roleTypes.contains(MedicineSignatureRoleTypeEnum.PHARMACY.getType())) {
                        idSet.add(medicine.getId());
                    }
                });

        return idSet;
    }

    private List<MedicineSignatureEntity> convert2SignatureList(ResidentInfoEntity residentInfo, Long medicineId,
                                                                List<MedicineSignatureParam> signatureList, Map<Long, Set<Integer>> unsignedMedicineMap) {
        return signatureList.stream()
                .filter(signature -> {
                    Set<Integer> roleTypeSet = unsignedMedicineMap.getOrDefault(medicineId, Collections.emptySet());
                    return !roleTypeSet.contains(signature.getRoleType());
                })
                .map(signature -> {
                    MedicineSignatureEntity signatureEntity = new MedicineSignatureEntity();
                    signatureEntity.setInstitutionId(residentInfo.getInstitutionId());
                    signatureEntity.setResidentId(residentInfo.getId());
                    signatureEntity.setMedicineId(medicineId);
                    signatureEntity.setSignatureImgUrl(signature.getSignatureImgUrl());
                    signatureEntity.setRoleType(signature.getRoleType());
                    return signatureEntity;
                }).toList();
    }

    @Override
    public List<MedicineSignatureVO> recentList(MedicineSignatureListParam param) {
        if (param == null) {
            return Collections.emptyList();
        }

        List<MedicineSignatureEntity> list = medicineSignatureService.selectDistinctSignatureImgUrl(
                param.getInstitutionId(), param.getResidentId(), param.getRoleType(), param.getSize()
        );
        return Optional.ofNullable(list).orElse(Collections.emptyList()).stream().map(MedicineSignatureVO::convert2VO).toList();
    }

}