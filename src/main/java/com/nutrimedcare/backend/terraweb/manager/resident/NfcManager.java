package com.nutrimedcare.backend.terraweb.manager.resident;

import com.nutrimedcare.backend.terraweb.param.nfc.NfcBindParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcCheckInConfirmParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcCheckInParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcTagDataParam;
import com.nutrimedcare.backend.terraweb.vo.nfc.NfcCheckInConfirmDataVO;

/**
 * <AUTHOR>
 */
public interface NfcManager {

    Boolean bindResident(NfcBindParam param);

    Boolean rebindResident(NfcBindParam param);

    Boolean checkBindRelation(NfcBindParam param);

    /**
     * 校验 NFC 卡片数据（加解密在客户端进行）
     */
    Boolean checkNfcTagData(NfcTagDataParam param);

    /**
     * 打卡确认数据
     */
    NfcCheckInConfirmDataVO residentCheckInDataDetail(NfcCheckInConfirmParam param);

    /**
     * 打卡
     */
    Boolean checkIn(NfcCheckInParam param);

}
