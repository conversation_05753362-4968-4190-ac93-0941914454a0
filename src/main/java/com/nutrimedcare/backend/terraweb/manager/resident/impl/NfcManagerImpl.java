package com.nutrimedcare.backend.terraweb.manager.resident.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nutrimedcare.backend.terraweb.common.BizErrorCode;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverRelatedEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.resident.ResidentCheckInInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.resident.ResidentNfcRelatedEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.manager.resident.NfcManager;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcBindParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcCheckInParam;
import com.nutrimedcare.backend.terraweb.param.nfc.NfcTagDataParam;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverInfoService;
import com.nutrimedcare.backend.terraweb.service.caregiver.CaregiverRelatedService;
import com.nutrimedcare.backend.terraweb.service.resident.ResidentCheckInInfoService;
import com.nutrimedcare.backend.terraweb.service.resident.ResidentNfcRelatedService;
import com.nutrimedcare.backend.terraweb.util.EncryptUtil;
import com.nutrimedcare.backend.terraweb.util.MedicineRecordTimeUtil;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineInfoVO;
import com.nutrimedcare.backend.terraweb.vo.nfc.NfcCheckInConfirmDataVO;
import com.nutrimedcare.backend.terraweb.vo.nfc.NfcCheckInVO;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NfcManagerImpl implements NfcManager {

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private ResidentNfcRelatedService residentNfcRelatedService;

    @Autowired
    private EncryptUtil encryptUtil;

    @Autowired
    private CaregiverRelatedService caregiverRelatedService;

    @Autowired
    private ResidentCheckInInfoService residentCheckInInfoService;

    @Autowired
    private CaregiverInfoService caregiverInfoService;

    @Autowired
    private MedicationRecordService medicationRecordService;
    @Autowired
    private CareInstitutionService careInstitutionService;

    @Override
    public Boolean bindResident(NfcBindParam param) {
        if (param == null) {
            return false;
        }

        residentNfcRelatedService.remove(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
                .or().eq(ResidentNfcRelatedEntity::getResidentId, param.getResidentId())
        );

        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getResidentId());

        List<ResidentNfcRelatedEntity> residentRelatedList = residentNfcRelatedService.list(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
        );
        if (CollUtil.isNotEmpty(residentRelatedList)) {
            throw new BusinessException("该 NFC 卡片已绑定");
        }

        boolean saveRes = residentNfcRelatedService.save(buildResidentNfcRelatedEntity(param, residentInfo.getInstitutionId()));
        if (!saveRes) {
            log.warn("nfc manager, fail to bind resident, param:{}", param);
        }
        return true;
    }

    private ResidentNfcRelatedEntity buildResidentNfcRelatedEntity(NfcBindParam param, Long institutionId) {
        ResidentNfcRelatedEntity entity = new ResidentNfcRelatedEntity();
        entity.setNfcUniqueId(param.getNfcUniqueId());
        entity.setInstitutionId(institutionId);
        entity.setResidentId(param.getResidentId());
        return entity;
    }

    @Override
    public Boolean rebindResident(NfcBindParam param) {
        if (param == null) {
            return false;
        }

        // 清除绑定信息
        residentNfcRelatedService.remove(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
        );

        return bindResident(param);
    }

    @Override
    public Boolean checkBindRelation(NfcBindParam param) {
        if (param == null) {
            return true;
        }
        List<ResidentNfcRelatedEntity> residentList = residentNfcRelatedService.list(new LambdaQueryWrapper<ResidentNfcRelatedEntity>()
                .eq(ResidentNfcRelatedEntity::getNfcUniqueId, param.getNfcUniqueId())
        );
        // 未存在绑定关系
        if (CollUtil.isEmpty(residentList)) {
            return false;
        }
        // 已存在绑定关系，但为当前入住人
        ResidentNfcRelatedEntity entity = residentList.get(0);
        return !Objects.equals(entity.getResidentId(), param.getResidentId());
    }

    @Override
    public Boolean checkNfcTagData(NfcTagDataParam param) {
        if (param == null) {
            return false;
        }

        try {
            String residentId = encryptUtil.aesDecrypt(param.getResidentIdStr());
            residentInfoService.findByIdAndCheckNull(Long.parseLong(residentId));
        } catch (Exception e) {
            return false;
        }

        // 校验通过，存在该入住人
        return true;
    }

    @Override
    public NfcCheckInConfirmDataVO residentCheckInDataDetail(NfcCheckInParam param) {
        // 查询入住人信息
        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getResidentId());
        // 查询机构信息
        CareInstitutionEntity caseInstitution = careInstitutionService.findByIdAndCheckNull(residentInfo.getInstitutionId());

        // 根据当前时间计算服药时间段
        Integer medicationTime = MedicineRecordTimeUtil.calMedicationTime(LocalDateTime.now(), caseInstitution.getExtInfo().getMedicationTakeTimeMap());

        // 查询入住人对应所有有效的用药记录
        List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                .eq(MedicationRecordEntity::getResidentId, param.getResidentId())
                .eq(MedicationRecordEntity::getValidStatus, BooleanEnum.TRUE.getType())
        );
        if (CollUtil.isEmpty(recordList)) {
            throw new BusinessException(BizErrorCode.NFC_CHECK_IN_NO_BIND_MEDICINE_RECORD);
        }

        // 根据服药时间段匹配用药记录
        List<MedicationRecordEntity> filterList = recordList.stream().filter(record -> {
            List<MedicineRecordDetailDTO> usageDetail = record.getUsageDetail();
            List<MedicineRecordDetailDTO> list = usageDetail.stream()
                    .filter(detail -> detail.getMedicationTimeList().contains(medicationTime)).toList();
            if (CollUtil.isEmpty(list)) {
                return false;
            }
            return true;
        }).toList();
        if (CollUtil.isEmpty(filterList)) {
            return buildNfcCheckInConfirmDataVO(medicationTime, Collections.emptyList());
        }

        // 根据用药记录获取用量、用量单位
        // - 优先从服药单的 usage 中获取，否则新服药单都会存在 snap ，如果不存在 snap，则获取当前药名对应的最新药品即可


        return buildNfcCheckInConfirmDataVO(medicationTime, Collections.emptyList());
    }


    private NfcCheckInConfirmDataVO buildNfcCheckInConfirmDataVO(Integer medicationTime, List<MedicineInfoVO> medicineList) {
        NfcCheckInConfirmDataVO vo = new NfcCheckInConfirmDataVO();
        vo.setMedicineTimeType(medicationTime);
        vo.setMedicineList(medicineList);
        return vo;
    }

    @Override
    public NfcCheckInVO checkIn(NfcCheckInParam param) {
        if (param == null) {
            return null;
        }

        ResidentInfoEntity residentInfo = residentInfoService.findByIdAndCheckNull(param.getResidentId());

        List<CaregiverRelatedEntity> caregiverList = caregiverRelatedService.list(new LambdaQueryWrapper<CaregiverRelatedEntity>()
                .eq(CaregiverRelatedEntity::getResidentId, residentInfo.getId())
                .eq(CaregiverRelatedEntity::getCaregiverId, param.getCaregiverId())
        );
        if (CollUtil.isEmpty(caregiverList)) {
            throw new BusinessException(BizErrorCode.NFC_CHECK_IN_NOT_BIND_CAREGIVER);
        }

        CaregiverInfoEntity caregiverInfo = caregiverInfoService.getById(param.getCaregiverId());


        List<ResidentCheckInInfoEntity> checkInDataList = residentCheckInInfoService.list(new LambdaQueryWrapper<ResidentCheckInInfoEntity>()
                .eq(ResidentCheckInInfoEntity::getResidentId, param.getResidentId())
                .in(ResidentCheckInInfoEntity::getMedicineName, param.getMedicineNameList())
        );
        if (CollUtil.isNotEmpty(checkInDataList)) {
            throw new BusinessException(BizErrorCode.NFC_CHECK_IN_REPEAT_TAKE);
        }

        boolean saveRes = residentCheckInInfoService.saveBatch(buildCheckInDataList(param, residentInfo, caregiverInfo));
        if (!saveRes) {
            log.info("nfc manager, fail to check in, param:{}", param);
        }
        return null;
    }


    private List<ResidentCheckInInfoEntity> buildCheckInDataList(NfcCheckInParam param, ResidentInfoEntity residentInfo, CaregiverInfoEntity caregiverInfo) {
        List<String> medicineNameList = param.getMedicineNameList();
        if (CollUtil.isEmpty(medicineNameList)) {
            return Collections.emptyList();
        }
        return medicineNameList.stream().map(medicineName -> buildCheckInDataEntity(medicineName, residentInfo, caregiverInfo)).toList();
    }

    private ResidentCheckInInfoEntity buildCheckInDataEntity(String medicineName, ResidentInfoEntity residentInfo, CaregiverInfoEntity caregiverInfo) {
        ResidentCheckInInfoEntity entity = new ResidentCheckInInfoEntity();

        entity.setMedicineName(medicineName);
        entity.setCheckInTime(LocalDateTime.now());
        entity.setMedicineTakeStatus(BooleanEnum.TRUE.getType());
        entity.setInstitutionId(residentInfo.getInstitutionId());

        entity.setResidentId(residentInfo.getId());
        entity.setLocationInfo(residentInfo.getLocationInfo());
        entity.setResidentName(residentInfo.getResidentName());
        entity.setResidentNamePinyin(residentInfo.getResidentNamePinyin());

        entity.setCaregiverId(caregiverInfo.getId());
        entity.setCaregiverDisplayId(caregiverInfo.getDisplayId());

        return entity;
    }
}
