package com.nutrimedcare.backend.terraweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MedicineInfoMapper extends BaseMapper<MedicineInfoEntity> {

    List<MedicineInfoEntity> findLastSameNameMedicine(@Param("residentId") Long residentId,
                                                      @Param("medicineNames") List<String> medicineNames,
                                                      @Param("writeOffStatus") Integer writeOffStatus
    );

    List<MedicineInfoEntity> findOldestSameNameMedicine(@Param("residentId") Long residentId,
                                                      @Param("medicineNames") List<String> medicineNames,
                                                      @Param("writeOffStatus") Integer writeOffStatus
    );

} 