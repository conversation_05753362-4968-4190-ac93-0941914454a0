package com.nutrimedcare.backend.terraweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MedicineSignature 数据访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicineSignatureMapper extends BaseMapper<MedicineSignatureEntity> {

    List<MedicineSignatureEntity> selectDistinctSignatureImgUrl(@Param("institutionId") Long institutionId, @Param("residentId") Long residentId,
                                                                @Param("roleType") Integer roleType, @Param("size") Integer size);

}