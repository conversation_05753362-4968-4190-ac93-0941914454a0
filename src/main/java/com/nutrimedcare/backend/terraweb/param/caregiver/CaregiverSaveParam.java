package com.nutrimedcare.backend.terraweb.param.caregiver;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CaregiverSaveParam {

    private Long id;

    @NotBlank(message = "护理员账号 ID 不能为空")
    @Size(min = 8, max = 50, message = "护理员账号 ID 长度不匹配")
    private String displayId;

    @NotBlank(message = "护理员密码不能为空")
    private String password;

    @NotEmpty(message = "护理员关联的入住人不能为空")
    private List<Long> relatedResidentIds;

    @NotNull(message = "机构ID不能为空")
    private Long institutionId;

}
