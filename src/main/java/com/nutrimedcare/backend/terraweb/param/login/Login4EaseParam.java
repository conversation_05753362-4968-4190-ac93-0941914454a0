package com.nutrimedcare.backend.terraweb.param.login;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Login4EaseParam {

    /**
     * 管理员和护理员都可以登录，不区分类型，所以 username 有两种可能
     * - institutionCode
     * - caregiver 的 displayId
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * AES 加密后的值
     */
    @NotBlank(message = "密码不能为空")
    private String password;

}