package com.nutrimedcare.backend.terraweb.param.login;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LoginParam {

    @NotBlank(message = "机构编号不能为空")
    @Size(min = 1, max = 20)
    @Pattern(regexp = "^[a-zA-Z0-9]+$", message = "机构号中不能有特殊符号")
    private String institutionCode;

    /**
     * AES 加密
     */
    @NotBlank(message = "密码不能为空")
    private String password;

}