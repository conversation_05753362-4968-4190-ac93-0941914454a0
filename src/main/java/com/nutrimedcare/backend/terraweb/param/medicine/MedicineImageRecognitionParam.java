package com.nutrimedcare.backend.terraweb.param.medicine;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicineImageRecognitionParam {

    @NotEmpty(message = "新增药品信息为空")
    private List<String> images;

    @NotNull
    private Long residentId;

    @NotNull
    private Integer medicineType;

}