package com.nutrimedcare.backend.terraweb.param.medicine;

import com.nutrimedcare.backend.terraweb.enums.medicine.MedicinePropertyEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class MedicineInfoList4ResidentParam {

    /**
     * @see MedicinePropertyEnum
     */
    @NotNull(message = "药品属性不能为空")
    private Integer medicineProperty;

    /**
     * @see MedicineTypeEnum
     */
    @NotNull(message = "药品类型不能为空")
    private Integer medicineType;

    @NotNull(message = "未匹配入住人信息")
    private Long residentId;

    @Range(min = 0, max = 1)
    private Integer needGroup;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.MedicineGroupStrategyEnum
     */
    @Range(min = 0, max = 2)
    private Integer groupStrategy;

}
