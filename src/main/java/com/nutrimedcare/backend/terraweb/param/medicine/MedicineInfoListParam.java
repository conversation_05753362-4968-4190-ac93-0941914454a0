package com.nutrimedcare.backend.terraweb.param.medicine;

import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineExpirationTypeEnum;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicineInfoListParam {

    @NotNull(message = "未匹配机构信息")
    private Long institutionId;

    /**
     * @see MedicineExpirationTypeEnum
     */
    @Range(min = 1, max = 3)
    private Integer expirationType;

    @Min(0)
    @NotNull
    private Integer pageNo;

    @Min(1)
    @NotNull
    private Integer pageSize;

    @NotEmpty(message = "排序条件为空")
    private List<MedicineInfoSortConditionParam> sortConditionList;

    private List<Long> medicineIds;

}
