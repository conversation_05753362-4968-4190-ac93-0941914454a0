package com.nutrimedcare.backend.terraweb.param.medicine;

import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineUsageTypeEnum;
import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class MedicineInfoSaveParam {

    private Long id;

    @NotNull(message = "入住人ID不能为空")
    private Long residentId;

    @NotNull(message = "登记时间不能为空")
    private LocalDateTime registerTime;

    @NotBlank(message = "药品名称不能为空")
    private String medicineName;

    /**
     * 如 100
     * 搭配单位字段使用，单位为 mg
     */
    @NotBlank(message = "规格不能为空")
    private String specification;

    /**
     * mg
     */
    @NotBlank(message = "规格单位不能为空")
    private String specificationUnit;

    @Future(message = "有效期必须是将来时间")
    private LocalDateTime expirationTime;

    private String manufacturerInfo;

    private String batchNumber;

    @NotBlank(message = "配药医院不能为空")
    private String hospitalInfo;

    @NotNull(message = "每盒含量不能为空")
    private Integer medicineContentPer;

    /**
     * 目前支持 '#'、'ml'
     */
//    @NotBlank(message = "含量单位不能为空")
    private String medicineContentPerUnit;

    @NotNull(message = "药品数量不能为空")
    private Integer medicineQuantity;

    @NotBlank(message = "药品数量单位不能为空")
    private String medicineQuantityUnit;

    @NotNull(message = "用量不能为空")
    private String dosage;

//    @NotNull(message = "用量单位不能为空")
    private String dosageUnit;

    /**
     * @see MedicineUsageTypeEnum
     */
    @NotNull(message = "用法不能为空")
    private Integer usageInfo;

    private MedicineExtInfoDTO extInfo;

} 