package com.nutrimedcare.backend.terraweb.param.medicine;

import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineSortTypeEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class MedicineInfoSortConditionParam {

    /**
     * @see MedicineSortTypeEnum
     */
    @Range(min = 1, max = 3)
    private Integer sortType;

    @Range(min = 0, max = 1)
    private Integer isAsc;

}
