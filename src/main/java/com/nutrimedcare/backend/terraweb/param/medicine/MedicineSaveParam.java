package com.nutrimedcare.backend.terraweb.param.medicine;

import com.nutrimedcare.backend.terraweb.enums.medicine.MedicinePropertyEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicineSaveParam {

    /**
     * @see MedicinePropertyEnum
     */
    @NotNull(message = "药品属性不能为空")
    private Integer medicineProperty;

    /**
     * @see MedicineTypeEnum
     */
    @NotNull(message = "药品类型不能为空")
    private Integer medicineType;

    @Valid
    @NotEmpty(message = "新增药品信息为空")
    private List<MedicineInfoSaveParam> infoList;

} 