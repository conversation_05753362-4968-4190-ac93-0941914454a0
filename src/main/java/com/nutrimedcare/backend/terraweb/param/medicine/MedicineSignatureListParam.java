package com.nutrimedcare.backend.terraweb.param.medicine;

import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class MedicineSignatureListParam {

    /**
     * @see MedicineSignatureRoleTypeEnum
     */
    @NotNull
    @Range(min = 1, max = 3)
    private Integer roleType;

    @NotNull
    @Range(min = 1, message = "最少展示一条")
    private Integer size;

    @NotNull(message = "未匹配到机构信息")
    private Long institutionId;

    private Long residentId;

}
