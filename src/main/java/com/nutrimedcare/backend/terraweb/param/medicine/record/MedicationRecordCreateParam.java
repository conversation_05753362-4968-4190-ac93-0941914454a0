package com.nutrimedcare.backend.terraweb.param.medicine.record;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicationRecordCreateParam {

    @NotNull(message = "未关联到入住人信息")
    private Long residentId;

    @Valid
    @NotEmpty(message = "未新增服药单信息")
    private List<MedicationRecordDetailParam> list;

}


