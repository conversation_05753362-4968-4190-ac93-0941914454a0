package com.nutrimedcare.backend.terraweb.param.medicine.record;

import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicationRecordDetailParam {

    private Long id;

    private Long medicineId;

    @NotNull
    private String medicineName;

    @Valid
    @NotEmpty
    private List<MedicineRecordDetailDTO> usageDetail;

}
