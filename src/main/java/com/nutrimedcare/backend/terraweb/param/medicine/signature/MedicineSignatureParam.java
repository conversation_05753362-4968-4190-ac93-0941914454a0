package com.nutrimedcare.backend.terraweb.param.medicine.signature;

import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class MedicineSignatureParam {

    /**
     * @see MedicineSignatureRoleTypeEnum
     */
    @NotNull(message = "签名类型不能为空")
    @Range(min = 1, max = 3, message = "不存在对应签名类型")
    private Integer roleType;

    @NotBlank(message = "签名信息不能为空")
    private String signatureImgUrl;

}
