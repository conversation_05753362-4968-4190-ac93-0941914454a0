package com.nutrimedcare.backend.terraweb.param.medicine.signature;

import com.nutrimedcare.backend.terraweb.enums.medicine.MedicinePropertyEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineTypeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicineSignatureSaveParam {

    @NotNull(message = "入住人id不能为空")
    private Long residentId;

    private List<Long> medicineIds;

    /**
     * @see MedicineTypeEnum
     */
    @NotNull(message = "药品类型不能为空")
    private Integer medicineType;

    /**
     * @see MedicinePropertyEnum
     */
    @NotNull(message = "药品属性不能为空")
    private Integer medicineProperty;

    /**
     * @see MedicineSignTypeEnum
     */
    private Integer signType;

    @Valid
    @Size(max = 3, message = "请检查签名信息列表")
    @NotEmpty(message = "签名信息不能为空")
    private List<MedicineSignatureParam> signatureList;
}