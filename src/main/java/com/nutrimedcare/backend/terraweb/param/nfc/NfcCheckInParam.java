package com.nutrimedcare.backend.terraweb.param.nfc;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class NfcCheckInParam {

    @NotNull
    private Long residentId;

    @NotNull
    private Long caregiverId;

    @NotEmpty
    private List<String> medicineNameList;

    /**
     * 服药状态
     *
     * @see com.nutrimedcare.backend.terraweb.enums.BooleanEnum
     */
    @NotNull
    private Integer takeStatus;

    @NotNull
    private Integer takeTimeType;

}
