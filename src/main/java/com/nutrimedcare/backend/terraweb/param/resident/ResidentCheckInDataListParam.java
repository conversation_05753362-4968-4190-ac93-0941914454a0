package com.nutrimedcare.backend.terraweb.param.resident;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ResidentCheckInDataListParam {

    @NotNull(message = "未匹配到机构信息")
    private Long institutionId;

    @Size(max = 20, message = "名字过长")
    private String residentName;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkInDate;

    @Range(min = 0, max = 1)
    private Integer takeStatus;

    @Size(min = 1, max = 50, message = "室号/床号过长")
    private String locationInfo;

    @Size(max = 50, message = "药品名过长")
    private String medicineName;

    @Size(max = 50, message = "护理员 Id 过长")
    private String caregiverDisplayId;

    @Min(0)
    @NotNull
    private Integer pageNo;

    @Min(10)
    @NotNull
    private Integer pageSize;
}