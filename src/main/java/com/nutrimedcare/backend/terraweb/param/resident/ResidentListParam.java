package com.nutrimedcare.backend.terraweb.param.resident;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class ResidentListParam {

    @NotNull(message = "未匹配到机构信息")
    private Long institutionId;

    @Size(max = 20, message = "名字过长")
    private String residentName;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.ResidentListShowSceneTypeEnum
     */
    @Range(min = 0, max = 2)
    private Integer showScene;

    /**
     * 用于 EASE APP
     */
    @Size(max = 20, message = "搜索条件过长")
    private String searchCondition;

}