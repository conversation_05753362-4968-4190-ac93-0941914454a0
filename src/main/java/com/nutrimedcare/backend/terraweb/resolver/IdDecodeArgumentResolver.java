package com.nutrimedcare.backend.terraweb.resolver;

import com.nutrimedcare.backend.terraweb.annotation.IdDecode;
import com.nutrimedcare.backend.terraweb.util.EncryptUtil;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR>
 */
public class IdDecodeArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(IdDecode.class) && parameter.getParameterType() == Long.class;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        String paramName = parameter.getParameterName();
        String value = webRequest.getParameter(paramName);
        if (value == null) {
            return null;
        }
        try {
            return Long.valueOf(EncryptUtil.base64Decrypt(value));
        } catch (Exception e) {
            throw new IllegalArgumentException("参数解密失败: " + paramName, e);
        }
    }

} 