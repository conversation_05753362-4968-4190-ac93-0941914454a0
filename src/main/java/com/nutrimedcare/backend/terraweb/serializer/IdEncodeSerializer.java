package com.nutrimedcare.backend.terraweb.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.nutrimedcare.backend.terraweb.util.EncryptUtil;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class IdEncodeSerializer extends JsonSerializer<Long> {

    @Override
    public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(EncryptUtil.base64Encrypt(value));
    }

}