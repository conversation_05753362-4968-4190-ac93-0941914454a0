package com.nutrimedcare.backend.terraweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface MedicationRecordService extends IService<MedicationRecordEntity> {

    Set<Long> listDistinctResidentIds();

    Map<Long, LocalDateTime> getLatestUpdateTimesMap(Set<Long> residentIds);

}