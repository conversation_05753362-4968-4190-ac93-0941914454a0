package com.nutrimedcare.backend.terraweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface MedicineInfoService extends IService<MedicineInfoEntity> {

    Map<Long, LocalDateTime> getLatestUpdateTimesMap(Set<Long> residentIds);

    List<MedicineInfoEntity> findLastSameNameMedicine(Long residentId, List<String> medicineNames, Integer writeOffStatus);

    List<MedicineInfoEntity> findOldestSameNameMedicine(Long residentId, List<String> medicineNames, Integer writeOffStatus);

}