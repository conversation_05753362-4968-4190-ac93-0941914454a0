package com.nutrimedcare.backend.terraweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.mapper.CareInstitutionMapper;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class CareInstitutionServiceImpl extends ServiceImpl<CareInstitutionMapper, CareInstitutionEntity>
        implements CareInstitutionService {

    @Override
    public CareInstitutionEntity findByIdAndCheckNull(Long id) {
        CareInstitutionEntity entity = this.getById(id);
        if (entity == null) {
            throw new BusinessException("institution info not found");
        }
        return entity;
    }

} 