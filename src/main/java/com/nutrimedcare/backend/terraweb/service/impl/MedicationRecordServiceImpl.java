package com.nutrimedcare.backend.terraweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.mapper.MedicationRecordMapper;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MedicationRecordServiceImpl extends ServiceImpl<MedicationRecordMapper, MedicationRecordEntity> implements MedicationRecordService {

    @Override
    public Set<Long> listDistinctResidentIds() {
        List<MedicationRecordEntity> list = this.list(
                new LambdaQueryWrapper<MedicationRecordEntity>()
                        .select(MedicationRecordEntity::getResidentId)
                        .groupBy(MedicationRecordEntity::getResidentId)
        );
        return Optional.ofNullable(list).orElse(Collections.emptyList())
                .stream()
                .map(MedicationRecordEntity::getResidentId)
                .collect(Collectors.toSet());
    }

    @Override
    public Map<Long, LocalDateTime> getLatestUpdateTimesMap(Set<Long> residentIds) {
        if (CollectionUtils.isEmpty(residentIds)) {
            return Collections.emptyMap();
        }

        List<Map<String, Object>> result = baseMapper.selectMaps(
                new QueryWrapper<MedicationRecordEntity>()
                        .select("resident_id, MAX(update_time) AS update_time")
                        .in("resident_id", residentIds)
                        .groupBy("resident_id")
        );

        return result.stream().collect(Collectors.toMap(
                map -> (Long) map.get("resident_id"),
                map -> (LocalDateTime) map.get("update_time"),
                (v1, v2) -> v2
        ));
    }

} 