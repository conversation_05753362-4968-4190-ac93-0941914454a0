package com.nutrimedcare.backend.terraweb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.mapper.MedicineInfoMapper;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MedicineInfoServiceImpl extends ServiceImpl<MedicineInfoMapper, MedicineInfoEntity> implements MedicineInfoService {

    @Override
    public Map<Long, LocalDateTime> getLatestUpdateTimesMap(Set<Long> residentIds) {
        if (CollectionUtils.isEmpty(residentIds)) {
            return Collections.emptyMap();
        }

        List<Map<String, Object>> result = baseMapper.selectMaps(
                new QueryWrapper<MedicineInfoEntity>()
                        .select("resident_id, MAX(update_time) AS update_time")
                        .in("resident_id", residentIds)
                        .groupBy("resident_id")
        );

        return result.stream().collect(Collectors.toMap(
                map -> (Long) map.get("resident_id"),
                map -> (LocalDateTime) map.get("update_time"),
                (v1, v2) -> v2
        ));
    }

    @Override
    public List<MedicineInfoEntity> findLastSameNameMedicine(Long residentId, List<String> medicineNames, Integer writeOffStatus) {
        if (residentId == null || CollectionUtils.isEmpty(medicineNames)) {
            return Collections.emptyList();
        }
        return baseMapper.findLastSameNameMedicine(residentId, medicineNames, writeOffStatus);
    }

    @Override
    public List<MedicineInfoEntity> findOldestSameNameMedicine(Long residentId, List<String> medicineNames, Integer writeOffStatus) {
        if (residentId == null || CollectionUtils.isEmpty(medicineNames)) {
            return Collections.emptyList();
        }
        return baseMapper.findOldestSameNameMedicine(residentId, medicineNames, writeOffStatus);
    }
}