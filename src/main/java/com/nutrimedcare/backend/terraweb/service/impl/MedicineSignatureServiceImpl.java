package com.nutrimedcare.backend.terraweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity;
import com.nutrimedcare.backend.terraweb.mapper.MedicineSignatureMapper;
import com.nutrimedcare.backend.terraweb.service.MedicineSignatureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MedicineSignatureServiceImpl extends ServiceImpl<MedicineSignatureMapper, MedicineSignatureEntity> implements MedicineSignatureService {

    @Autowired
    private MedicineSignatureMapper medicineSignatureMapper;

    @Override
    public List<MedicineSignatureEntity> selectDistinctSignatureImgUrl(Long institutionId, Long residentId, Integer roleType, Integer size) {
        return medicineSignatureMapper.selectDistinctSignatureImgUrl(institutionId, residentId, roleType, size);
    }

}