package com.nutrimedcare.backend.terraweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.exception.BusinessException;
import com.nutrimedcare.backend.terraweb.mapper.ResidentInfoMapper;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ResidentInfoServiceImpl extends ServiceImpl<ResidentInfoMapper, ResidentInfoEntity> implements ResidentInfoService {

    @Override
    public ResidentInfoEntity findByIdAndCheckNull(Long id) {
        ResidentInfoEntity entity = this.getById(id);
        if (entity == null) {
            throw new BusinessException("resident info not found");
        }
        return entity;
    }

}