package com.nutrimedcare.backend.terraweb.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineExpirationTypeEnum;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.util.MedicineUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class InstitutionScoreCalculateTask {

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    /**
     * (1 * 安全药数 + 0.5 * 临期药数) / 总药数量
     */
    @Scheduled(cron = "0 1 0 * * ? ")
    public void task() {
        List<CareInstitutionEntity> institutionList = careInstitutionService.list();
        if (CollectionUtils.isEmpty(institutionList)) {
            return;
        }

        List<CareInstitutionEntity> updateList = institutionList.stream()
                .map(institution -> {
                            CareInstitutionEntity entity = new CareInstitutionEntity();
                            entity.setId(institution.getId());
                            entity.setScore(calScore(institution));
                            entity.setUpdateTime(LocalDateTime.now());
                            return entity;
                        }
                ).toList();
        boolean updateRes = careInstitutionService.updateBatchById(updateList);
        log.info("update institution score success, updateList.size:{}, updateList:{}, update res:{}", institutionList.size(), institutionList, updateRes);
    }

    private Integer calScore(CareInstitutionEntity entity) {
        List<MedicineInfoEntity> list = medicineInfoService.list(
                new LambdaQueryWrapper<MedicineInfoEntity>()
                        .eq(MedicineInfoEntity::getInstitutionId, entity.getId())
        );
        if (CollectionUtils.isEmpty(list)) {
            return 100;
        }

        int safeCount = 0;
        int nearExpiryCount = 0;
        int expiredCount = 0;

        for (MedicineInfoEntity info : list) {
            if (info.getWriteOffStatus() == 1) {
                continue;
            }
            Integer expirationType = MedicineUtil.calExpirationType(info.getExpirationTime());
            if (MedicineExpirationTypeEnum.NO_EXPIRATION.getType().equals(expirationType)) {
                safeCount++;
            } else if (MedicineExpirationTypeEnum.EXPIRING_SOON.getType().equals(expirationType)) {
                nearExpiryCount++;
            } else if (MedicineExpirationTypeEnum.EXPIRED.getType().equals(expirationType)) {
                expiredCount++;
            }
        }

        int total = list.size();
        if (total == 0) {
            return 0;
        }

        return (int) Math.floor((1.0 * safeCount + 0.5 * nearExpiryCount) / total * 100);
    }

}
