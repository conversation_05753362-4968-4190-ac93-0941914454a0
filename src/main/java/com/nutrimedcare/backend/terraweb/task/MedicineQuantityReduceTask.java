package com.nutrimedcare.backend.terraweb.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import com.nutrimedcare.backend.terraweb.util.FractionUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.math.Fraction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 药品数量自动减少
 */
@Slf4j
@Component
public class MedicineQuantityReduceTask {

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private MedicineStockService medicineStockService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Scheduled(cron = "0 0 1 * * ? ")
    public void task() {
        List<CareInstitutionEntity> allInstitutions = careInstitutionService.list();
        if (CollUtil.isEmpty(allInstitutions)) {
            return;
        }
        allInstitutions.forEach(institution -> dealMedicineStock(institution.getId()));
    }

    private void dealMedicineStock(Long institutionId) {
        int pageNo = 1;
        Page<MedicineStockEntity> page;

        do {
            page = medicineStockService.page(
                    new Page<>(pageNo, 50),
                    new LambdaQueryWrapper<MedicineStockEntity>().eq(MedicineStockEntity::getInstitutionId, institutionId)
            );

            page.getRecords().forEach(stockInfo -> {
                // 判断服药单是否有效
                List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                        .eq(MedicationRecordEntity::getResidentId, stockInfo.getResidentId())
                        .eq(MedicationRecordEntity::getMedicineName, stockInfo.getMedicineName())
                );
                if (CollUtil.isEmpty(recordList)
                        || BooleanEnum.FALSE.getType().equals(recordList.get(0).getValidStatus())
                        || judgeNotEat(recordList.get(0).getUsageDetail())
                ) {
                    log.warn("medicine quantity reduce task, fail to find record info or record is invalid, stockInfo:{}", stockInfo);
                    return;
                }

                Fraction stock = FractionUtil.convert2Fraction(stockInfo.getMedicineStock());
                Fraction totalUseNum = FractionUtil.convert2Fraction(stockInfo.getTotalUseNum());

                Fraction dailyConsumeNum = FractionUtil.convert2Fraction(stockInfo.getDailyConsumeNum());

                Fraction subtractRes = stock.subtract(dailyConsumeNum);
                Fraction addRes = totalUseNum.add(dailyConsumeNum);

                medicineStockService.update(new LambdaUpdateWrapper<MedicineStockEntity>()
                        .set(MedicineStockEntity::getMedicineStock, judgePositiveNumber(subtractRes) ? FractionUtil.convertFromFraction(subtractRes) : "0-0-0")
                        .set(MedicineStockEntity::getTotalUseNum, FractionUtil.convertFromFraction(addRes))
                        .eq(MedicineStockEntity::getId, stockInfo.getId())
                );
            });

            // 下一个循环
            pageNo += 1;
        } while (page.hasNext());
    }

    private boolean judgeNotEat(List<MedicineRecordDetailDTO> usageDetail) {
        for (MedicineRecordDetailDTO detail : usageDetail) {
            List<Integer> medicationTimeList = detail.getMedicationTimeList();
            if (CollUtil.isEmpty(medicationTimeList) || medicationTimeList.contains(MedicationRecordTimeTypeEnum.NO_EAT.getType())) {
                return true;
            }
        }
        return false;
    }

    private boolean judgePositiveNumber(Fraction subtractRes) {
        String properString = subtractRes.toProperString();
        return !properString.startsWith("-") && !"0".equals(properString);
    }

}
