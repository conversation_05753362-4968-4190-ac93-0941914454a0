package com.nutrimedcare.backend.terraweb.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.service.CareInstitutionService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import com.nutrimedcare.backend.terraweb.util.FractionUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.math.Fraction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 药品数量自动减少
 */
@Slf4j
@Component
public class MedicineWriteOffReduceTask {

    @Autowired
    private CareInstitutionService careInstitutionService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicineStockService medicineStockService;

    @Scheduled(cron = "0 0 5 * * ? ")
    public void task() {
        List<CareInstitutionEntity> allInstitutions = careInstitutionService.list();
        if (CollUtil.isEmpty(allInstitutions)) {
            return;
        }
        allInstitutions.forEach(institution -> dealMedicineStock(institution.getId()));
    }

    private void dealMedicineStock(Long institutionId) {
        int pageNo = 1;
        Page<MedicineStockEntity> page;
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();

        do {
            page = medicineStockService.page(
                    new Page<>(pageNo, 50),
                    new LambdaQueryWrapper<MedicineStockEntity>().eq(MedicineStockEntity::getInstitutionId, institutionId)
            );

            page.getRecords().forEach(stockInfo -> {
                // 根据 stockInfo 查询药品信息
                List<MedicineInfoEntity> medicineList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                        .eq(MedicineInfoEntity::getInstitutionId, institutionId)
                        .eq(MedicineInfoEntity::getResidentId, stockInfo.getResidentId())
                        .eq(MedicineInfoEntity::getMedicineName, stockInfo.getMedicineName())
                        .ge(MedicineInfoEntity::getExpirationTime, startOfDay)
                        .eq(MedicineInfoEntity::getWriteOffStatus, BooleanEnum.FALSE.getType())
                        .orderByAsc(MedicineInfoEntity::getId)
                );
                if (CollUtil.isEmpty(medicineList)) {
                    return;
                }

                MedicineInfoEntity updateEntity = medicineList.get(0);
                Fraction medicineContentPer = FractionUtil.convert2Fraction(updateEntity.getMedicineContentPer() + "-0-0");
                Fraction totalUseNum = FractionUtil.convert2Fraction(stockInfo.getTotalUseNum());
                if (judgeEnough(medicineContentPer, totalUseNum)) {
                    return;
                }
                // 核销药品
                medicineInfoService.update(new LambdaUpdateWrapper<MedicineInfoEntity>()
                        .set(MedicineInfoEntity::getWriteOffStatus, BooleanEnum.TRUE.getType())
                        .eq(MedicineInfoEntity::getId, updateEntity.getId())
                );
                // 更新 stock 的使用量
                medicineStockService.update(new LambdaUpdateWrapper<MedicineStockEntity>()
                        .set(MedicineStockEntity::getTotalUseNum, FractionUtil.convertFromFraction(totalUseNum.subtract(medicineContentPer)))
                        .eq(MedicineStockEntity::getId, stockInfo.getId())
                );
            });

            // 下一个循环
            pageNo += 1;
        } while (page.hasNext());
    }

    private boolean judgeEnough(Fraction medicineContentPer, Fraction totalUseNum) {
        Fraction subtract = medicineContentPer.subtract(totalUseNum);
        String properString = subtract.toProperString();
        return !properString.startsWith("-") && !"0".equals(properString);
    }

}
