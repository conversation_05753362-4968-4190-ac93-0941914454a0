package com.nutrimedcare.backend.terraweb.third.impl;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrimedcare.backend.terraweb.dto.third.MedicineImageRecognitionResp;
import com.nutrimedcare.backend.terraweb.dto.third.ThirdResponse;
import com.nutrimedcare.backend.terraweb.third.ThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdServiceImpl implements ThirdService {

    private static final String MEDICINE_IMAGE_RECOGNITION_URI = "/nutrimed-ai/medbox/ocrByOss";
    private static final String MULTI_MEDICINE_IMAGE_RECOGNITION_URI = "/nutrimed-ai/medbox/v2/ocrByOss/multi";
    private final WebClient webClient;
    @Autowired
    private ObjectMapper objectMapper;

    public ThirdServiceImpl(
            WebClient.Builder webClientBuilder,
            @Value("${third.medicine-image-recognition.base-url:http://8.153.83.39:8000}") String baseUrl
    ) {
        this.webClient = webClientBuilder.baseUrl(baseUrl).build();
    }

    @Override
    public List<MedicineImageRecognitionResp> medicineImageRecognition(List<String> images) {
        return Flux.fromIterable(images)
                .flatMap(image -> {
                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("url", image);
                    return webClient.post()
                            .uri(MEDICINE_IMAGE_RECOGNITION_URI)
                            .header("Content-Type", "application/json")
                            .bodyValue(requestBody)
                            .retrieve()
                            .bodyToMono(new ParameterizedTypeReference<ThirdResponse<MedicineImageRecognitionResp>>() {
                            })
                            .doOnSuccess(val -> {
                                try {
                                    log.info("Medicine Image Recognition Response: " + objectMapper.writeValueAsString(val));
                                } catch (JsonProcessingException e) {
                                    log.error("fail to log medicine image recognition response", e);
                                }
                            })
                            .onErrorReturn(new ThirdResponse<>());
                })
                .collectList()
                .map(resultList -> resultList.stream()
                        .filter(ThirdResponse::isSuccess)
                        .map(ThirdResponse::getData)
                        .toList())
                .block();

    }

    @Override
    public List<MedicineImageRecognitionResp> multiMedicineImageRecognition(String image) {
        List<List<MedicineImageRecognitionResp>> resList = Flux.just(image)
                .flatMap(img -> {
                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("url", img);
                    return webClient.post()
                            .uri(MULTI_MEDICINE_IMAGE_RECOGNITION_URI)
                            .header("Content-Type", "application/json")
                            .bodyValue(requestBody)
                            .retrieve()
                            .bodyToMono(new ParameterizedTypeReference<ThirdResponse<List<MedicineImageRecognitionResp>>>() {
                            })
                            .doOnSuccess(val -> {
                                try {
                                    log.info("Multi Medicine Image Recognition Response: " + objectMapper.writeValueAsString(val));
                                } catch (JsonProcessingException e) {
                                    log.error("fail to log medicine image recognition response", e);
                                }
                            })
                            .onErrorReturn(new ThirdResponse<>());
                })
                .collectList()
                .map(resultList -> resultList.stream()
                        .filter(ThirdResponse::isSuccess)
                        .map(ThirdResponse::getData)
                        .toList())
                .block();
        return CollUtil.isNotEmpty(resList) ? resList.get(0) : Collections.emptyList();
    }
}
