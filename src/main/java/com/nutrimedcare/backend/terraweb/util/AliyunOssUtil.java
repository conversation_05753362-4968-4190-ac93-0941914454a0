package com.nutrimedcare.backend.terraweb.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AliyunOssUtil {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    @Value("${aliyun.oss.urlPrefix}")
    private String urlPrefix;

    /**
     * 上传文件到 OSS
     *
     * @param inputStream 文件流
     * @param fileName    文件名
     * @return 文件访问 URL
     */
    public String uploadFile(InputStream inputStream, String fileName, String dicPrefix) {
        // 生成唯一文件名，防止重复
        String objectName = UUID.randomUUID().toString().replace("-", "") + "_" + fileName;

        // 创建 OSSClient 实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 上传文件流
            ossClient.putObject(bucketName, dicPrefix + objectName, inputStream);
            // 返回文件访问 URL
            return urlPrefix + dicPrefix + objectName;
        } catch (Exception e) {
            log.error("上传文件到 OSS 失败", e);
            return null;
        } finally {
            ossClient.shutdown(); // 关闭 OSSClient
        }
    }

    /**
     * 删除 OSS 文件
     *
     * @param fileUrl 文件访问 URL
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(urlPrefix)) {
            return false;
        }

        String objectName = fileUrl.replace(urlPrefix, "");
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            ossClient.deleteObject(bucketName, objectName);
            return true;
        } catch (Exception e) {
            log.error("删除 OSS 文件失败", e);
            return false;
        } finally {
            ossClient.shutdown();
        }
    }
}