package com.nutrimedcare.backend.terraweb.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.symmetric.AES;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class EncryptUtil {

    public static final String SECRET_KEY = "++nutrimedcare2025";

    private static final String PREFIX = "NC";

    private AES aes;

    @Value("${aes.key}")
    private String aesKey;

    @Value("${aes.iv}")
    private String aesIv;

    public static String base64Encrypt(Long data) {
        if (data == null) {
            return null;
        }
        return PREFIX + Base64.getEncoder().encodeToString((data + SECRET_KEY).getBytes()).replace("=", "");
    }

    public static String base64Decrypt(String encryptedData) {
        if (encryptedData == null) {
            return null;
        }
        String finalData = encryptedData.substring(2);
        String decoded = new String(Base64.getDecoder().decode(finalData));
        return decoded.replace(SECRET_KEY, "");
    }

    public Map<String, String> generateKeyAndIv() {
        // 生成 16 字节随机密钥和 IV
        byte[] keyBytes = RandomUtil.randomBytes(16);
        byte[] ivBytes = RandomUtil.randomBytes(16);

        // 转换为 Base64 编码
        String keyBase64 = Base64.getEncoder().encodeToString(keyBytes);
        String ivBase64 = Base64.getEncoder().encodeToString(ivBytes);

        // 返回结果
        Map<String, String> result = new HashMap<>();
        result.put("key", keyBase64);
        result.put("iv", ivBase64);
        return result;
    }

    public String aesEncrypt(String data) {
        return data == null ? null : aes.encryptBase64(data);
    }

    public String aesDecrypt(String encryptedData) {
        if (encryptedData == null) {
            return null;
        }
        try {
            return aes.decryptStr(encryptedData);
        } catch (Exception e) {
            throw new RuntimeException("解密失败: " + e.getMessage());
        }
    }

    @PostConstruct
    public void init() {
        aes = new AES(
                "CBC",
                "PKCS5Padding",
                aesKey.getBytes(StandardCharsets.UTF_8),
                aesIv.getBytes(StandardCharsets.UTF_8)
        );
    }

}