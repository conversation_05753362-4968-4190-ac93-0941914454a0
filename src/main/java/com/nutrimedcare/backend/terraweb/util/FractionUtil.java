package com.nutrimedcare.backend.terraweb.util;

import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.math.Fraction;

/**
 * <AUTHOR>
 */
@Slf4j
public class FractionUtil {

    private static final String FRACTION_ZERO_STR = "0-0-0";

    private FractionUtil() {
    }

    public static Fraction convert2Fraction(String dosage) {
        try {
            if (FRACTION_ZERO_STR.equals(dosage)) {
                return Fraction.ZERO;
            }
            String[] split = dosage.split("-");

            if ("0".equals(split[2])) {
                return Fraction.of(Integer.parseInt(split[0]), 1);
            }
            return Fraction.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]), Integer.parseInt(split[2]));
        } catch (Exception e) {
            log.error("fail to convert2Fraction, num:{}", dosage, e);
            return Fraction.ZERO;
        }
    }

    public static String convertFromFraction(Fraction stock) {
        if (stock == null || stock.equals(Fraction.ZERO)) {
            return FRACTION_ZERO_STR;
        }

        // e.g., "2 1/2", "1/2", "5"
        String properString = stock.toProperString();

        if (!properString.contains(" ") && !properString.contains("/")) {
            // It's a whole number, e.g., "5"
            return String.format("%s-0-0", properString);
        } else if (!properString.contains(" ")) {
            // It's a proper fraction without a whole part, e.g., "1/2"
            String[] parts = properString.split("/");
            return String.format("0-%s-%s", parts[0], parts[1]);
        } else {
            // It's a mixed fraction, e.g., "2 1/2"
            String[] wholeAndFraction = properString.split(" ");
            String wholePart = wholeAndFraction[0];
            String[] fractionParts = wholeAndFraction[1].split("/");
            return String.format("%s-%s-%s", wholePart, fractionParts[0], fractionParts[1]);
        }
    }

}
