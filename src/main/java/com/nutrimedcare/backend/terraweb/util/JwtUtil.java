package com.nutrimedcare.backend.terraweb.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import javax.crypto.SecretKey;
import java.util.Base64;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class JwtUtil {

    private static final String SECRET_KEY_STRING = "Kfq6cjOdSBk3pjS4SSgoKvG4Xo2YQIiSDFHn8QxMR/4=";

    // token 有效期 60 年
    private static final long EXPIRE_DURATION = 60L * 24 * 60 * 60 * 1000;
    // token 有效期 100 年
    private static final long PERMANENT_EXPIRE_DURATION = 3650L * 24 * 60 * 60 * 1000;

    private static final SecretKey SECRET_KEY = Keys.hmacShaKeyFor(Base64.getDecoder().decode(SECRET_KEY_STRING));

    public static void main(String[] args) {
        SecretKey key = Keys.secretKeyFor(SignatureAlgorithm.HS256);
        String base64Key = Base64.getEncoder().encodeToString(key.getEncoded());
        System.out.println("Base64 Encoded Key: " + base64Key);
    }

    public static boolean validateToken(String token) {
        try {
            Jwts.parserBuilder().setSigningKey(SECRET_KEY).build().parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(SECRET_KEY)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public static String generateToken(String institutionName, String institutionCode) {
        return Jwts.builder()
                .setSubject(String.format("%s,%s", institutionName, institutionCode))
                .setIssuer("nutrimedcare")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRE_DURATION))
                .signWith(SECRET_KEY)
                .compact();
    }

    public static String generateToken(String bizId, String bizType, String appType) {
        return Jwts.builder()
                .setSubject(String.format("%s,%s,%s", bizId, bizType, appType))
                .setIssuer("nutrimedcare")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + PERMANENT_EXPIRE_DURATION))
                .signWith(SECRET_KEY)
                .compact();
    }

} 