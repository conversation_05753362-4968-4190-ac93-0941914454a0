package com.nutrimedcare.backend.terraweb.util;

import com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.lang.tuple.Pair;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class MedicineRecordTimeUtil {

    /**
     * 默认服药时间段配置
     * key: MedicationRecordTimeTypeEnum的type值
     * value: 时间段范围 [开始时间, 结束时间)
     */
    private static final Map<Integer, Pair<String, String>> DEFAULT_MEDICATION_TIME_MAP = new HashMap<>();

    static {
        // 1: 早餐前 [6:00, 7:00)
        DEFAULT_MEDICATION_TIME_MAP.put(MedicationRecordTimeTypeEnum.BEFORE_BREAKFAST.getType(),
                Pair.of("06:00", "07:00"));
        // 2: 早餐后 [7:00, 8:00)
        DEFAULT_MEDICATION_TIME_MAP.put(MedicationRecordTimeTypeEnum.AFTER_BREAKFAST.getType(),
                Pair.of("07:00", "08:00"));
        // 3: 午餐前 [10:00, 11:00)
        DEFAULT_MEDICATION_TIME_MAP.put(MedicationRecordTimeTypeEnum.BEFORE_LUNCH.getType(),
                Pair.of("10:00", "11:00"));
        // 4: 午餐后 [11:00, 12:00)
        DEFAULT_MEDICATION_TIME_MAP.put(MedicationRecordTimeTypeEnum.AFTER_LUNCH.getType(),
                Pair.of("11:00", "12:00"));
        // 5: 晚餐前 [16:00, 17:00)
        DEFAULT_MEDICATION_TIME_MAP.put(MedicationRecordTimeTypeEnum.BEFORE_DINNER.getType(),
                Pair.of("16:00", "17:00"));
        // 6: 晚餐后 [17:00, 18:00)
        DEFAULT_MEDICATION_TIME_MAP.put(MedicationRecordTimeTypeEnum.AFTER_DINNER.getType(),
                Pair.of("17:00", "18:00"));
        // 7: 睡前 [19:30, 20:30)
        DEFAULT_MEDICATION_TIME_MAP.put(MedicationRecordTimeTypeEnum.BEFORE_SLEEP.getType(),
                Pair.of("19:30", "20:30"));
    }

    private MedicineRecordTimeUtil() {
    }

    /**
     * 根据当前时间以及时间段匹配规则计算当前服药时间段
     *
     * @param now                   当前时间
     * @param medicationTakeTimeMap 服药时间段配置，如果为空则使用默认配置
     * @return 匹配的服药时间段类型，如果没有匹配到则返回null
     */
    public static Integer calMedicationTime(LocalDateTime now, Map<Integer, Pair<String, String>> medicationTakeTimeMap) {
        if (now == null) {
            log.warn("calMedicationTime: now is null");
            return null;
        }

        // 如果传入的配置为空，使用默认配置
        Map<Integer, Pair<String, String>> timeMap = CollUtil.isEmpty(medicationTakeTimeMap)
                ? DEFAULT_MEDICATION_TIME_MAP
                : medicationTakeTimeMap;

        LocalTime currentTime = now.toLocalTime();

        // 遍历时间段配置，找到匹配的时间段
        for (Map.Entry<Integer, Pair<String, String>> entry : timeMap.entrySet()) {
            Integer timeType = entry.getKey();
            Pair<String, String> timeRange = entry.getValue();

            if (timeRange == null || timeRange.getLeft() == null || timeRange.getRight() == null) {
                continue;
            }

            try {
                LocalTime startTime = LocalTime.parse(timeRange.getLeft());
                LocalTime endTime = LocalTime.parse(timeRange.getRight());

                // 判断当前时间是否在时间段内
                if (isTimeInRange(currentTime, startTime, endTime)) {
                    log.info("calMedicationTime: matched time type {}, current time: {}, range: [{}, {})",
                            timeType, currentTime, startTime, endTime);
                    return timeType;
                }
            } catch (Exception e) {
                log.warn("calMedicationTime: failed to parse time range for type {}, range: {}",
                        timeType, timeRange, e);
            }
        }

        log.warn("calMedicationTime: no matching time range found for current time: {}", currentTime);
        return null;
    }

    /**
     * 判断时间是否在指定范围内
     * 支持跨天的时间段（如22:00-02:00）
     *
     * @param currentTime 当前时间
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 是否在范围内
     */
    private static boolean isTimeInRange(LocalTime currentTime, LocalTime startTime, LocalTime endTime) {
        if (startTime.equals(endTime)) {
            return false;
        }

        // 正常情况：开始时间 < 结束时间（如07:00-09:00）
        if (startTime.isBefore(endTime)) {
            return !currentTime.isBefore(startTime) && currentTime.isBefore(endTime);
        }
        // 跨天情况：开始时间 > 结束时间（如22:00-02:00）
        else {
            return !currentTime.isBefore(startTime) || currentTime.isBefore(endTime);
        }
    }

}