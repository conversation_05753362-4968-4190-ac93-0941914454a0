package com.nutrimedcare.backend.terraweb.util;

import cn.hutool.core.lang.Validator;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineExpirationTypeEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MedicineUtil {

    private static final String MEDICINE_CODE_PREFIX = "N";
    private static final Map<String, String> MEDICINE_SPECIFICATION_UNIT_MAP = Map.of(
            "千克", "kg",
            "毫克", "mg",
            "微克", "µg",
            "纳克", "ng",
            "克", "g",
            "微升", "µl",
            "毫升", "ml",
            "升", "L"
    );
    private final Random random = new Random();
    @Getter
    private final Set<String> todayMedicineCodeSet = ConcurrentHashMap.newKeySet();

    /**
     * judge expiration type by expiration date
     * |    Expired    |          ExpiringSoon            |       NoExpiration          |
     * ---------------now ---------------------------- 180 <USER> <GROUP> expirationDate
     */
    public static Integer calExpirationType(LocalDateTime expirationTime) {
        if (expirationTime == null) {
            return null;
        }
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        if (expirationTime.isBefore(startOfDay) || Objects.equals(expirationTime, startOfDay)) {
            return MedicineExpirationTypeEnum.EXPIRED.getType();
        }
        if (expirationTime.isAfter(startOfDay.plusDays(180).plusMinutes(-1))) {
            return MedicineExpirationTypeEnum.NO_EXPIRATION.getType();
        }
        return MedicineExpirationTypeEnum.EXPIRING_SOON.getType();
    }

    public static String parseMedicineSpecificationUnit(String medicineUnit) {
        String specificationUnit = medicineUnit.replaceAll("[\\d.]", "");

        if (Validator.isChinese(specificationUnit)) {
            return MEDICINE_SPECIFICATION_UNIT_MAP.entrySet().stream()
                    .filter(entry -> medicineUnit.contains(entry.getKey()))
                    .map(Map.Entry::getValue)
                    .findFirst()
                    .orElse("");
        }

        if (Validator.hasChinese(specificationUnit)) {
            return "";
        }

        return specificationUnit;
    }

    /**
     * 以厄贝沙坦片为例，前面登记了10盒，那么在整理成表格的时候会拆分成10条数据，并且给每一盒赋予药品编号
     * 药品编号命名规则:以N2503210001为例，N固定、登记日期(250302)、系统自动生成后四位(0001)
     */
    public String generateMedicineCode(LocalDateTime now) {
        if (now == null) {
            return "";
        }
        String registerDate = now.format(DateTimeFormatter.BASIC_ISO_DATE).substring(2);
        return MEDICINE_CODE_PREFIX + registerDate + generateUniqueNumber();
    }

    public String generateUniqueNumber() {
        String uniqueNum;
        do {
            uniqueNum = String.format("%04d", random.nextInt(1, 10000));
        } while (!todayMedicineCodeSet.add(uniqueNum));
        return uniqueNum;
    }

    @Scheduled(cron = "0 0 0 * * ?")
    public void resetDaily() {
        todayMedicineCodeSet.clear();
        log.info("reset daily medicine codes");
    }

} 