package com.nutrimedcare.backend.terraweb.vo.caregiver;

import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverInfoEntity;
import com.nutrimedcare.backend.terraweb.vo.resident.ResidentInfoVO;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CaregiverDetailVO {

    private Long id;

    private String displayId;

    private String password;

    private List<ResidentInfoVO> relatedResidentList;

    public static CaregiverDetailVO convert2VO(CaregiverInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        CaregiverDetailVO vo = new CaregiverDetailVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
