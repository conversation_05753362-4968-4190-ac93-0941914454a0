package com.nutrimedcare.backend.terraweb.vo.caregiver;

import com.nutrimedcare.backend.terraweb.dao.caregiver.CaregiverInfoEntity;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
@Data
public class CaregiverListVO {

    private Long id;

    private String displayId;

    public static CaregiverListVO convert2VO(CaregiverInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        CaregiverListVO vo = new CaregiverListVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
