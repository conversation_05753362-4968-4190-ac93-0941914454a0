package com.nutrimedcare.backend.terraweb.vo.institution;

import com.nutrimedcare.backend.terraweb.dao.CareInstitutionEntity;
import com.nutrimedcare.backend.terraweb.dto.institution.CareInstitutionExtInfoDTO;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class InstitutionDetailVO {

    private Long id;

    private String institutionCode;

    private String institutionName;

    private String institutionNameAbbr;

    private String logoUrl;

    private Integer score;

    private LocalDateTime updateTime;

    private CareInstitutionExtInfoDTO extInfo;

    private InstitutionRelatedVO relatedVO;

    public static InstitutionDetailVO convertToVO(CareInstitutionEntity entity, InstitutionRelatedVO relatedVO) {
        if (entity == null) {
            return null;
        }
        InstitutionDetailVO vo = new InstitutionDetailVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setExtInfo(entity.getExtInfo());
        vo.setRelatedVO(relatedVO);
        return vo;
    }

} 