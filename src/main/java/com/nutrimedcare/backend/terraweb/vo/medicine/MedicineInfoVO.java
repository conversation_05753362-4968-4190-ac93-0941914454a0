package com.nutrimedcare.backend.terraweb.vo.medicine;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineRunOutSoonLogEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineInfoDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRunOutSoonExtInfoDTO;
import com.nutrimedcare.backend.terraweb.dto.third.MedicineImageRecognitionResp;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineSpecificationUnitTypeEnum;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineUsageTypeEnum;
import com.nutrimedcare.backend.terraweb.util.MedicineUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MedicineInfoVO {

    private LocalDateTime registerTime;

    private String medicineName;

    /**
     * 如 100
     * 搭配单位字段使用，单位为 mg
     */
    private String specification;

    /**
     * mg
     */
    private String specificationUnit;

    private LocalDateTime expirationTime;

    private String manufacturerInfo;

    private String batchNumber;

    private String hospitalInfo;

    private Integer medicineContentPer;

    private String medicineContentPerUnit;

    private Integer medicineQuantity;

    /**
     * 目前支持：盒
     */
    private String medicineQuantityUnit;

    private String dosage;

    private String dosageUnit;

    private Integer usageInfo;

    private String medicineCode;

    /*
     *                  以上为图像识别结果
     ************************************************************************
     */

    private Long id;

    private Long residentId;

    private String residentName;

    private String locationInfo;

    private Integer writeOffStatus;

    private Integer expirationType;

    /**
     * @see com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO
     */
    private MedicineExtInfoDTO extInfo;

    /**
     * 分数形式
     * 假设 20片，用了 1/4，存储为 19-3-4
     */
    private String medicineStock;

    /**
     * 对应签字
     */
    private List<MedicineSignatureVO> medicineSignatureList;

    private Long recordId;

    private Integer usageIndex;

    /**
     * data of medication record
     */
    private List<Integer> medicationTimeList;

    private MedicineRunOutSoonExtInfoDTO runOutSoonExtInfo;

    public static MedicineInfoVO convert2VO(MedicineInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        MedicineInfoVO vo = new MedicineInfoVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setExpirationType(MedicineUtil.calExpirationType(entity.getExpirationTime()));
        vo.setExtInfo(entity.getExtInfo());
        return vo;
    }

    public static MedicineInfoVO convert2VO(MedicineRunOutSoonLogEntity entity) {
        if (entity == null) {
            return null;
        }
        MedicineInfoVO vo = new MedicineInfoVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setRunOutSoonExtInfo(entity.getExtInfo());
        return vo;
    }


    public static MedicineInfoVO convert2VO(MedicineInfoDTO dto) {
        if (dto == null) {
            return null;
        }
        MedicineInfoVO vo = new MedicineInfoVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    public static MedicineInfoVO convert2VO(MedicineImageRecognitionResp resp) {
        if (resp == null) {
            return new MedicineInfoVO();
        }
        MedicineInfoVO vo = new MedicineInfoVO();
        vo.setMedicineName(resp.getName());
        vo.setExpirationTime(LocalDateTimeUtil.parse(resp.getExpireDate(), "yyyy-MM-dd"));
        vo.setBatchNumber(resp.getLotNum());
        vo.setManufacturerInfo(resp.getManufacturer());

        vo.setSpecification(resp.getSpecificationValue());
        vo.setSpecificationUnit(MedicineSpecificationUnitTypeEnum.isValid(resp.getSpecificationUnit()) ? resp.getSpecificationUnit() : null);

        vo.setHospitalInfo(resp.getDispensingInstitution());
        vo.setUsageInfo(MedicineUsageTypeEnum.convertFromStr(resp.getFrequency()));

        String packageSize = resp.getPackageSize();
        if (packageSize != null) {
            vo.setMedicineContentPer(NumberUtil.parseInt(packageSize.replaceAll("[^\\d.]", "")));
        }

        String dose = resp.getDose();
        if (dose != null && vo.getSpecification() == null && vo.getSpecificationUnit() == null) {
            // 提取数字部分作为 specification
            String specification = dose.replaceAll("[^\\d.]", "");
            // 提取单位部分作为 specificationUnit
            String specificationUnit = MedicineUtil.parseMedicineSpecificationUnit(dose);

            vo.setSpecification(specification);
            vo.setSpecificationUnit(specificationUnit);

//            vo.setDosage(specification);
//            vo.setDosageUnit(MedicineDosageUnitTypeEnum.isValid(specificationUnit) ? specificationUnit : null);
        }

        return vo;
    }

    public static MedicineInfoVO convert2VO(MedicineImageRecognitionResp resp, MedicineInfoEntity entity) {
        MedicineInfoVO vo = MedicineInfoVO.convert2VO(resp);
        if (entity == null) {
            return vo;
        }

        // check param
        if (vo.getUsageInfo() != null && !MedicineUsageTypeEnum.isValid(vo.getUsageInfo())) {
            vo.setUsageInfo(MedicineUsageTypeEnum.QD.getType());
        }

        // fill blank field
        if (StringUtils.isBlank(vo.getSpecification())) {
            vo.setSpecification(entity.getSpecification());
        }
        if (StringUtils.isBlank(vo.getSpecificationUnit())) {
            vo.setSpecificationUnit(entity.getSpecificationUnit());
        }
        if (StringUtils.isBlank(vo.getBatchNumber())) {
            vo.setBatchNumber(entity.getBatchNumber());
        }
        if (StringUtils.isBlank(vo.getManufacturerInfo())) {
            vo.setManufacturerInfo(entity.getManufacturerInfo());
        }
        if (StringUtils.isBlank(vo.getHospitalInfo())) {
            vo.setHospitalInfo(entity.getHospitalInfo());
        }
        if (vo.getMedicineContentPer() == null) {
            vo.setMedicineContentPer(entity.getMedicineContentPer());
        }
        if (vo.getMedicineContentPerUnit() == null) {
            vo.setMedicineContentPerUnit(entity.getMedicineContentPerUnit());
        }
        if (vo.getMedicineQuantity() == null) {
            vo.setMedicineQuantity(entity.getMedicineQuantity());
        }
        if (StringUtils.isBlank(vo.getMedicineQuantityUnit())) {
            vo.setMedicineQuantityUnit(entity.getMedicineQuantityUnit());
        }
        if (StringUtils.isBlank(vo.getDosage())) {
            vo.setDosage(entity.getDosage());
        }
        if (StringUtils.isBlank(vo.getDosageUnit())) {
            vo.setDosageUnit(entity.getDosageUnit());
        }
        if (vo.getUsageInfo() == null) {
            vo.setUsageInfo(entity.getUsageInfo());
        }
        if (vo.getExtInfo() == null) {
            vo.setExtInfo(entity.getExtInfo());
        }

        return vo;
    }

}
