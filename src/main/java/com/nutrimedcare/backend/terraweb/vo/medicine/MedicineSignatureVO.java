package com.nutrimedcare.backend.terraweb.vo.medicine;

import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity;
import com.nutrimedcare.backend.terraweb.enums.medicine.signature.MedicineSignatureRoleTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MedicineSignatureVO {

    /**
     * @see MedicineSignatureRoleTypeEnum
     */
    private Integer roleType;

    private String signatureImgUrl;

    public static MedicineSignatureVO convert2VO(MedicineSignatureEntity entity) {
        MedicineSignatureVO vo = new MedicineSignatureVO();
        vo.setRoleType(entity.getRoleType());
        vo.setSignatureImgUrl(entity.getSignatureImgUrl());
        return vo;
    }

}
