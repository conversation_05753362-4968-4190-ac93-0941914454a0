package com.nutrimedcare.backend.terraweb.vo.medicine.record;

import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineSpecificationDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class MedicationRecordVO {

    private Long id;

    private Long medicineId;

    private String medicineName;

    /**
     * 可用状态
     *
     * @see com.nutrimedcare.backend.terraweb.enums.BooleanEnum
     */
    private Integer validStatus;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.medicine.record.MedicationRecordTimeTypeEnum
     */
    private List<MedicationRecordDetailVO> recordList;

    /*
     ************************* 药品信息数据 *************************
     */

    private String residentName;

    private String locationInfo;

    private String specificationInfo;

    private List<MedicineSpecificationDTO> specificationInfoList;


    public static MedicationRecordVO convert2VO(MedicationRecordEntity entity, List<Pair<String, String>> dosageList) {
        MedicationRecordVO vo = new MedicationRecordVO();

        BeanUtils.copyProperties(entity, vo);

        // 失效服药单需要重新保存
        if (!BooleanEnum.TRUE.getType().equals(entity.getValidStatus())) {
            vo.setId(null);
        }

        List<MedicineRecordDetailDTO> usageDetail = entity.getUsageDetail();
        if (CollUtil.isEmpty(usageDetail)) {
            return vo;
        }

        // 用法数量不一致，说明药品有变化，例如删除，不展示
        if (!Objects.equals(dosageList.size(), usageDetail.size())) {
            return null;
        }

        List<MedicationRecordDetailVO> voList = new ArrayList<>();
        for (int i = 0; i < usageDetail.size(); i++) {
            MedicineRecordDetailDTO detail = usageDetail.get(i);
            Pair<String, String> pair = dosageList.get(i);
            if (detail != null && pair != null) {
                voList.add(MedicationRecordDetailVO.convert2VO(detail, pair.getKey(), pair.getValue()));
            }
        }
        vo.setRecordList(voList);

        return vo;
    }

}
