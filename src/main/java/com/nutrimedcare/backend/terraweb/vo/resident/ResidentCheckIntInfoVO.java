package com.nutrimedcare.backend.terraweb.vo.resident;

import com.nutrimedcare.backend.terraweb.dao.resident.ResidentCheckInInfoEntity;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ResidentCheckIntInfoVO {

    private Long id;

    private LocalDateTime checkInTime;

    private String locationInfo;

    private String residentName;

    private String medicineName;

    private String caregiverDisplayId;

    /**
     * 服药状态
     *
     * @see com.nutrimedcare.backend.terraweb.enums.BooleanEnum
     */
    private Integer medicineTakeStatus;

    public static ResidentCheckIntInfoVO convert2VO(ResidentCheckInInfoEntity entity) {
        if (entity == null) {
            return null;
        }
        ResidentCheckIntInfoVO vo = new ResidentCheckIntInfoVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

}