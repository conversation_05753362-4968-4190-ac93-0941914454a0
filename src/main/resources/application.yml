spring:
  profiles:
    active: ${spring.profiles.active:dev}
  application:
    name: terra-web
  jackson:
    default-property-inclusion: non_null
  servlet:
      multipart:
        max-file-size: 20MB
        max-request-size: 20MB
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  show-actuator: false
  packages-to-scan: com.nutrimedcare.backend.terraweb.controller
aes:
  key: nutrimedcare2025
  iv: nutrimedcare2025