<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 从 application.yml 读取 logging.log-path -->
    <springProperty name="LOG_PATH" source="logging.log-path" defaultValue="/Users/<USER>/Documents/PersonalSpace/external-project/nutrimed-care/code/terra-web/log" />
    <!-- 从 application.yml 读取 logging.file-name -->
    <springProperty name="FILE_NAME" source="logging.file-name" defaultValue="app" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 按天滚动的文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天滚动，生成文件名如 app.2025-04-15.log -->
            <fileNamePattern>${LOG_PATH}/${FILE_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留 30 天的日志 -->
            <maxHistory>30</maxHistory>
            <!-- 日志文件最大 10GB -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>