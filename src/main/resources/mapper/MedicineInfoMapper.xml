<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nutrimedcare.backend.terraweb.mapper.MedicineInfoMapper">
    <resultMap id="MedicineInfoResultMap" type="com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity">
        <!-- 假设 'id' 是主键 -->
        <id property="id" column="id"/>
        <result property="extInfo" column="ext_info"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="findLastSameNameMedicine" resultMap="MedicineInfoResultMap">
        SELECT *
        FROM (
        SELECT *, ROW_NUMBER() OVER (PARTITION BY medicine_name ORDER BY update_time DESC) as rn
        FROM tb_medicine_info
        WHERE resident_id = #{residentId} AND medicine_name IN
        <foreach item="name" collection="medicineNames" open="(" separator="," close=")">
            #{name}
        </foreach>
        <if test="writeOffStatus != null">
            AND write_off_status = #{writeOffStatus}
        </if>
        AND deleted = 0
        ) AS ranked_medicines
        WHERE rn = 1
    </select>

    <select id="findOldestSameNameMedicine" resultMap="MedicineInfoResultMap">
        SELECT *
        FROM (
        SELECT *, ROW_NUMBER() OVER (PARTITION BY medicine_name ORDER BY update_time ASC) as rn
        FROM tb_medicine_info
        WHERE resident_id = #{residentId} AND medicine_name IN
        <foreach item="name" collection="medicineNames" open="(" separator="," close=")">
            #{name}
        </foreach>
        <if test="writeOffStatus != null">
            AND write_off_status = #{writeOffStatus}
        </if>
        AND deleted = 0
        ) AS ranked_medicines
        WHERE rn = 1
    </select>
</mapper>