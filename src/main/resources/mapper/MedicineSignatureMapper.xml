<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nutrimedcare.backend.terraweb.mapper.MedicineSignatureMapper">

    <select id="selectDistinctSignatureImgUrl"
            resultType="com.nutrimedcare.backend.terraweb.dao.medicine.MedicineSignatureEntity">
        SELECT *
        FROM (
        SELECT *, ROW_NUMBER() OVER (PARTITION BY signature_img_url ORDER BY create_time DESC) AS rn
        FROM tb_medicine_signature
        WHERE role_type = #{roleType}
        AND institution_id = #{institutionId}
        <if test="residentId != null">
            AND resident_id = #{residentId}
        </if>
        ) t
        WHERE rn = 1
        ORDER BY create_time DESC
        LIMIT #{size}
    </select>

</mapper>