package com.nutrimedcare.backend.terraweb;

import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.service.MedicineRunOutSoonLogService;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class NutriMedCareApplicationTests {

    @Autowired
    private MedicineStockService medicineStockService;

    @Autowired
    private MedicineRunOutSoonLogService medicineRunOutSoonLogService;

    @Test
    void contextLoads() {
        MedicineStockEntity stockEntity = new MedicineStockEntity();


        medicineStockService.save(stockEntity);
    }

}
