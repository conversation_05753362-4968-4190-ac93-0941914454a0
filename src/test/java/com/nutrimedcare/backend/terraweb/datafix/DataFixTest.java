package com.nutrimedcare.backend.terraweb.datafix;

import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class DataFixTest {

    @Autowired
    private ResidentInfoService residentInfoService;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Test
    public void residentLocationDataFix() {
        List<ResidentInfoEntity> residentList = residentInfoService.list();
        List<ResidentInfoEntity> updateList = residentList.stream().map(resident -> {
            ResidentInfoEntity entity = new ResidentInfoEntity();
            entity.setId(resident.getId());
            entity.setLocationInfo(resident.getLocationInfo().replace("/", "-"));
            return entity;
        }).toList();
        residentInfoService.updateBatchById(updateList);

        List<MedicineInfoEntity> medicineList = medicineInfoService.list();
        List<MedicineInfoEntity> medicineUpdateList = medicineList.stream().map(medicine -> {
            MedicineInfoEntity entity = new MedicineInfoEntity();
            entity.setId(medicine.getId());
            entity.setLocationInfo(medicine.getLocationInfo().replace("/", "-"));
            return entity;
        }).toList();
        medicineInfoService.updateBatchById(medicineUpdateList);
    }

    @Test
    public void medicineIdentifierDataFix() {
        List<MedicineInfoEntity> list = medicineInfoService.list();

        list.forEach(entity -> {
            if (entity.getExtInfo() == null) {
                return;
            }
            MedicineExtInfoDTO extInfo = entity.getExtInfo();
            extInfo.setInitNum(entity.getMedicineContentPer());
            extInfo.setStock(convert2Stock(entity.getMedicineContentPer()));
            entity.setExtInfo(extInfo);
        });

        medicineInfoService.updateBatchById(list);
    }

    private String convert2Stock(Integer contentPer) {
        if (contentPer == null) {
            return "0-0-0";
        }
        return contentPer + "-0-0";
    }

}
