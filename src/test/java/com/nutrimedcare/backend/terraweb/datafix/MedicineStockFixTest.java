package com.nutrimedcare.backend.terraweb.datafix;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineExtInfoDTO;
import com.nutrimedcare.backend.terraweb.enums.BooleanEnum;
import com.nutrimedcare.backend.terraweb.service.MedicationRecordService;
import com.nutrimedcare.backend.terraweb.service.MedicineInfoService;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import com.nutrimedcare.backend.terraweb.task.MedicineQuantityReduceTask;
import com.nutrimedcare.backend.terraweb.task.MedicineRunOutSoonStatTask;
import com.nutrimedcare.backend.terraweb.task.MedicineWriteOffReduceTask;
import com.nutrimedcare.backend.terraweb.util.FractionUtil;
import com.nutrimedcare.backend.terraweb.util.UsageInfoUtil;
import org.dromara.hutool.core.math.Fraction;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@SpringBootTest
class MedicineStockFixTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MedicineInfoService medicineInfoService;

    @Autowired
    private MedicineStockService medicineStockService;

    @Autowired
    private MedicationRecordService medicationRecordService;

    @Autowired
    private MedicineRunOutSoonStatTask medicineRunOutSoonStatTask;

    @Autowired
    private MedicineQuantityReduceTask medicineQuantityReduceTask;

    @Autowired
    private MedicineWriteOffReduceTask medicineWriteOffReduceTask;

    @Test
    void medicineStockFix() {

        // 为每一个有效的服药单创建一个库存记录
        List<MedicationRecordEntity> recordList = medicationRecordService.list(new LambdaQueryWrapper<MedicationRecordEntity>()
                .eq(MedicationRecordEntity::getValidStatus, BooleanEnum.TRUE.getType()));

        recordList.forEach(record -> {

            Long residentId = record.getResidentId();
            String medicineName = record.getMedicineName();

            List<MedicineInfoEntity> medicineInfoList = medicineInfoService.list(new LambdaQueryWrapper<MedicineInfoEntity>()
                    .eq(MedicineInfoEntity::getResidentId, residentId)
                    .eq(MedicineInfoEntity::getMedicineName, medicineName)
                    .orderByDesc(MedicineInfoEntity::getId)
            );


            MedicineInfoEntity lastMedicine = medicineInfoList.get(0);

            int totalNum = medicineInfoList.stream()
                    .map(MedicineInfoEntity::getExtInfo)
                    .filter(Objects::nonNull)
                    .map(MedicineExtInfoDTO::getInitNum)
                    .filter(Objects::nonNull)
                    .reduce(0, Integer::sum);


            Fraction totalStock = medicineInfoList.stream()
                    .filter(entity -> !BooleanEnum.TRUE.getType().equals(entity.getWriteOffStatus()))
                    .map(MedicineInfoEntity::getExtInfo)
                    .filter(Objects::nonNull)
                    .map(MedicineExtInfoDTO::getStock)
                    .filter(Objects::nonNull)
                    .map(FractionUtil::convert2Fraction)
                    .reduce(Fraction.ZERO, Fraction::add);


            MedicineStockEntity stockEntity = new MedicineStockEntity();
            stockEntity.setInstitutionId(1L);
            stockEntity.setResidentId(residentId);
            stockEntity.setMedicineId(lastMedicine.getId());
            stockEntity.setMedicineName(medicineName);
            stockEntity.setTotalNum(totalNum + "-0-0");
            stockEntity.setMedicineStock(FractionUtil.convertFromFraction(totalStock));
            stockEntity.setTotalUseNum("0-0-0");
//            stockEntity.setWeeklyConsumeNum(FractionUtil.convertFromFraction(UsageInfoUtil.calWeekConsumeNum(lastMedicine)));
//            stockEntity.setDailyConsumeNum(FractionUtil.convertFromFraction(UsageInfoUtil.calDailyConsumeNum(lastMedicine, Collections.emptyMap())));

            try {
                medicineStockService.save(stockEntity);
                System.out.println(objectMapper.writeValueAsString(stockEntity));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

        });

    }


    @Test
    void runOutSoonTaskTest() {
        medicineRunOutSoonStatTask.task();
    }

    @Test
    void medicineQuantityReduceTaskTest() {
        medicineQuantityReduceTask.task();
    }

    @Test
    void medicineWriteOffReduceTaskTest() {
        medicineWriteOffReduceTask.task();
    }
}
