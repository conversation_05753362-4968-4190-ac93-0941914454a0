package com.nutrimedcare.backend.terraweb.excel;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrimedcare.backend.terraweb.common.CommonSymbol;
import com.nutrimedcare.backend.terraweb.dao.ResidentInfoEntity;
import com.nutrimedcare.backend.terraweb.enums.ResidentGenderEnum;
import com.nutrimedcare.backend.terraweb.service.ResidentInfoService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@SpringBootTest
class DataImportTest {

    private static final Logger log = LoggerFactory.getLogger(DataImportTest.class);
    @Autowired
    private ResidentInfoService residentInfoService;
    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void residentLocationDataFix() {
        String fileName = "/Users/<USER>/Desktop/和养机构入住人表模板.xlsx";
        EasyExcel.read(fileName, ResidentInfoEImportDTO.class, new PageReadListener<ResidentInfoEImportDTO>(dataList -> {

            List<ResidentInfoEntity> saveList = dataList.stream().map(this::convert2ResidentInfo).toList();
            List<ResidentInfoEntity> exsitList = residentInfoService.list(new LambdaQueryWrapper<ResidentInfoEntity>()
                    .in(ResidentInfoEntity::getResidentName, saveList.stream().map(ResidentInfoEntity::getResidentName).toList())
            );
            Set<String> existNames = exsitList.stream().map(ResidentInfoEntity::getResidentName).collect(Collectors.toSet());

            List<ResidentInfoEntity> filterList = saveList.stream().filter(residentInfoEntity -> !existNames.contains(residentInfoEntity.getResidentName())).toList();
            residentInfoService.saveBatch(filterList);

            try {
                log.info("save list:{}", objectMapper.writeValueAsString(saveList));
                log.info("exsit list:{}", objectMapper.writeValueAsString(exsitList));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }


        })).sheet().doRead();


    }

    private ResidentInfoEntity convert2ResidentInfo(ResidentInfoEImportDTO demoData) {
        ResidentInfoEntity residentInfo = new ResidentInfoEntity();
        residentInfo.setInstitutionId(20L);
        residentInfo.setResidentName(demoData.getName());
        residentInfo.setResidentNamePinyin(PinyinUtil.getPinyin(demoData.getName()));
        residentInfo.setGender("男".equals(demoData.getGender()) ? ResidentGenderEnum.MALE.getType() : ResidentGenderEnum.FEMALE.getType());
        residentInfo.setBirthdate(convert2LocalDateTime(demoData.getDate()));
        residentInfo.setLocationInfo(StringUtils.joinWith(CommonSymbol.HYPHEN_SYMBOL, demoData.getRoomNum(), demoData.getBedNum()));
        return residentInfo;
    }

    private LocalDateTime convert2LocalDateTime(Date oldDate) {
        if (oldDate == null) {
            return null;
        }

        // 2. 将 Date 转换为 Instant
        Instant instant = oldDate.toInstant();

        // 3. 获取当前系统的默认时区 (或者指定一个时区，例如 ZoneId.of("Asia/Shanghai"))
        ZoneId systemDefaultZone = ZoneId.of("Asia/Shanghai"); // 例如：Asia/Shanghai

        // 4. 使用 Instant 和 ZoneId 转换为 LocalDateTime
        LocalDateTime newLocalDateTime = LocalDateTime.ofInstant(instant, systemDefaultZone);

//        System.out.println("Original Date: " + oldDate);
//        System.out.println("Converted LocalDateTime: " + newLocalDateTime);

        // 示例：指定一个时区
        ZoneId newYorkZone = ZoneId.of("Asia/Shanghai");
        LocalDateTime newYorkLocalDateTime = LocalDateTime.ofInstant(instant, newYorkZone);
//        System.out.println("Converted LocalDateTime in New York: " + newYorkLocalDateTime);
        return newYorkLocalDateTime;
    }

}
