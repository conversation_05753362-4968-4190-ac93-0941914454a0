package com.nutrimedcare.backend.terraweb.manager;

import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineManager;
import com.nutrimedcare.backend.terraweb.param.medicine.MedicineImageRecognitionParam;
import com.nutrimedcare.backend.terraweb.vo.medicine.MedicineImageRecognitionVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

@SpringBootTest
class MedicineInfoManagerTest {

    @Autowired
    private MedicineManager medicineManager;

    @Test
    void imageRecognitionTest() {
        MedicineImageRecognitionParam param = new MedicineImageRecognitionParam();
        param.setImages(
                Arrays.asList(
                        "https://nutrimedcare-test.oss-cn-shanghai.aliyuncs.com/medicineRecognition/0d3c10209f384d55b5a5b1f9d61abda5_298901744191536_.pic_hd.jpg",
                        "https://nutrimedcare-test.oss-cn-shanghai.aliyuncs.com/medicineRecognition/b3c2c70117364c15b34d811ee94968cc_298911744191540_.pic_hd.jpg",
                        "https://nutrimedcare-test.oss-cn-shanghai.aliyuncs.com/medicineRecognition/2e464dbe9c174f4ca169baa837a14855_298921744191544_.pic_hd.jpg"
                )
        );
        MedicineImageRecognitionVO vo = medicineManager.imageRecognition(param);
        System.out.println(vo.toString());
    }
}
