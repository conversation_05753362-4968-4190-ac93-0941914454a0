package com.nutrimedcare.backend.terraweb.manager;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.manager.medicine.MedicineRecordManager;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import com.nutrimedcare.backend.terraweb.task.InstitutionScoreCalculateTask;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest
class MedicineStockCalTest {

    @Autowired
    private MedicineRecordManager medicineRecordManager;

    @Test
    void medicineRecordManagerTest() {
//        medicineRecordManager.calMedicineStock(1L, "hahah");
    }

}
