package com.nutrimedcare.backend.terraweb.manager;

import com.nutrimedcare.backend.terraweb.param.resident.ResidentSaveParam;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class ResidentInfoManagerTest {

    @Autowired
    private ResidentInfoManager residentInfoManager;

    @Test
    void residentSaveTest() {
        ResidentSaveParam param = new ResidentSaveParam();
        param.setInstitutionId(1L);
        param.setResidentName("test");
        param.setBirthdate(LocalDateTime.now().minusYears(50));
        param.setGender(1);
        param.setRoomNum("101");
        param.setBedNum("1");
        residentInfoManager.save(param);
    }
}
