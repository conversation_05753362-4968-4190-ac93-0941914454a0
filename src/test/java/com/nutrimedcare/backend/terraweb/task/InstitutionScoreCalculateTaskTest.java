package com.nutrimedcare.backend.terraweb.task;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineStockEntity;
import com.nutrimedcare.backend.terraweb.service.MedicineStockService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 */
@SpringBootTest
class InstitutionScoreCalculateTaskTest {

    @Autowired
    private InstitutionScoreCalculateTask institutionScoreCalculateTask;

    @Autowired
    private MedicineStockService medicineStockService;

    @Test
    void scoreCalculateTaskTest() {
        institutionScoreCalculateTask.task();
    }

    @Test
    void medicineRunOutSoonStatTaskTest() {
        medicineStockService.update(new LambdaUpdateWrapper<MedicineStockEntity>()
                .set(MedicineStockEntity::getTotalUseNum, "30-0-0")
                .eq(MedicineStockEntity::getId, 1L)
        );
    }


}
