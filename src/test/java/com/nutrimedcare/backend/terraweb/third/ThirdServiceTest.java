package com.nutrimedcare.backend.terraweb.third;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nutrimedcare.backend.terraweb.dto.third.MedicineImageRecognitionResp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

@SpringBootTest
public class ThirdServiceTest {

    @Autowired
    private ThirdService thirdService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void medicineImageRecognitionTest() throws JsonProcessingException {
        List<String> images = Arrays.asList(
                "https://nutrimedcare-test.oss-cn-shanghai.aliyuncs.com/medicineRecognition/0d3c10209f384d55b5a5b1f9d61abda5_298901744191536_.pic_hd.jpg",
                "https://nutrimedcare-test.oss-cn-shanghai.aliyuncs.com/medicineRecognition/b3c2c70117364c15b34d811ee94968cc_298911744191540_.pic_hd.jpg",
                "https://nutrimedcare-test.oss-cn-shanghai.aliyuncs.com/medicineRecognition/2e464dbe9c174f4ca169baa837a14855_298921744191544_.pic_hd.jpg"
        );
        List<MedicineImageRecognitionResp> recognizeList = thirdService.medicineImageRecognition(images);
        System.out.println(objectMapper.writeValueAsString(recognizeList));
    }

    @Test
    public void multiMedicineImageRecognitionTest() throws JsonProcessingException {
        String image = "https://nutrimedcare-test.oss-cn-shanghai.aliyuncs.com/medicineRecognition/edf91d9b6e9544c3a8e569bdb9c943e7_image.jpg";
        List<MedicineImageRecognitionResp> recognizeList = thirdService.multiMedicineImageRecognition(image);
        System.out.println(objectMapper.writeValueAsString(recognizeList));
    }

}
