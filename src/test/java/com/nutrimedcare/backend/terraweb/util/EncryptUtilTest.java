package com.nutrimedcare.backend.terraweb.util;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

@SpringBootTest
public class EncryptUtilTest {

    @Autowired
    private EncryptUtil encryptUtil;

    @Test
    public void base64Encrypt() {
        System.out.println(EncryptUtil.base64Encrypt(15L));
    }

    @Test
    public void base64Decrypt() {
        System.out.println(EncryptUtil.base64Decrypt("NCMTUrK251dHJpbWVkY2FyZTIwMjU"));
    }

    @Test
    public void generateSecretKey() {
        Map<String, String> x = encryptUtil.generateKeyAndIv();
        System.out.println(x);
        System.out.println(new String(Base64.getDecoder().decode(x.get("key")), StandardCharsets.UTF_8));
        System.out.println(new String(Base64.getDecoder().decode(x.get("iv")), StandardCharsets.UTF_8));
    }

    @Test
    public void aesEncrypt() {
        System.out.println(encryptUtil.aesEncrypt("236"));
    }

    @Test
    public void aesDecrypt() {
        System.out.println(encryptUtil.aesDecrypt("Nz1s5ExRIMt3BXbD/Pkm2A=="));
    }

}
