package com.nutrimedcare.backend.terraweb.util;

import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineExpirationTypeEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.logging.Logger;

@SpringBootTest
class MedicineUtilTest {

    private final Logger log = Logger.getLogger(MedicineUtilTest.this.getClass().getSimpleName());

    @Autowired
    private MedicineUtil medicineUtil;

    @Test
    void calExpirationTypeTest() {
        LocalDateTime expiredTime = LocalDateTime.now().toLocalDate().atStartOfDay().plusDays(180).plusMinutes(0);
        System.out.println(expiredTime);
        Integer type = MedicineUtil.calExpirationType(expiredTime);
        System.out.println(Objects.requireNonNull(MedicineExpirationTypeEnum.getByType(type)).getDesc());
    }

    @Test
    void todayNumPoolTest() {
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < 10; i++) {
            log.info("test medicine util, generateMedicineCode:" + medicineUtil.generateMedicineCode(now));
        }
        Assertions.assertFalse(medicineUtil.getTodayMedicineCodeSet().isEmpty());

        medicineUtil.resetDaily();
        Assertions.assertTrue(medicineUtil.getTodayMedicineCodeSet().isEmpty());
    }

    @Test
    void generateMedicineCodeTest() {
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < 10; i++) {
            log.info("test medicine util, generateMedicineCode:" + medicineUtil.generateMedicineCode(now));
        }
        log.info("test medicine util, generate medicine code, set:" + medicineUtil.getTodayMedicineCodeSet());
    }

}
